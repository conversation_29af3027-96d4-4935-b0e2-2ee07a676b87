'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';

interface VitrineData {
  _id: string;
  businessName: string;
  businessDescription: string;
  businessImages: string[];
  socialMedia: {
    facebook: string;
    instagram: string;
    twitter: string;
    linkedin: string;
    website: string;
  };
  coordinates: {
    latitude: number;
    longitude: number;
  };
  businessCategory: string;
  businessTags: string[];
  isVitrineActive: boolean;
  phone: string;
  address: string;
  services: string[];
}

interface VitrineManagerProps {
  vitrineData: VitrineData;
}

export default function VitrineManager({ vitrineData }: VitrineManagerProps) {
  const [formData, setFormData] = useState({
    businessDescription: vitrineData.businessDescription,
    businessCategory: vitrineData.businessCategory,
    businessTags: vitrineData.businessTags.join(', '),
    socialMedia: vitrineData.socialMedia,
    coordinates: vitrineData.coordinates,
    isVitrineActive: vitrineData.isVitrineActive,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (name.startsWith('socialMedia.')) {
      const platform = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        socialMedia: {
          ...prev.socialMedia,
          [platform]: value,
        },
      }));
    } else if (name.startsWith('coordinates.')) {
      const coord = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        coordinates: {
          ...prev.coordinates,
          [coord]: parseFloat(value) || 0,
        },
      }));
    } else if (type === 'checkbox') {
      setFormData(prev => ({
        ...prev,
        [name]: (e.target as HTMLInputElement).checked,
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/settings/vitrine', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          businessTags: formData.businessTags.split(',').map(tag => tag.trim()).filter(tag => tag),
        }),
      });

      if (response.ok) {
        setMessage('Site vitrine mis à jour avec succès');
        if (formData.isVitrineActive) {
          setTimeout(() => {
            window.open(`/vitrine/${vitrineData._id}`, '_blank');
          }, 1000);
        }
      } else {
        const error = await response.json();
        setMessage(error.message || 'Erreur lors de la mise à jour');
      }
    } catch (error) {
      console.error('Erreur:', error);
      setMessage('Erreur lors de la mise à jour');
    } finally {
      setIsLoading(false);
    }
  };

  const getAddressFromCoordinates = async () => {
    if (!formData.coordinates.latitude || !formData.coordinates.longitude) {
      setMessage('Veuillez entrer des coordonnées valides');
      return;
    }

    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${formData.coordinates.latitude},${formData.coordinates.longitude}&key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}`
      );
      const data = await response.json();
      
      if (data.results && data.results[0]) {
        const address = data.results[0].formatted_address;
        setMessage(`Adresse trouvée: ${address}`);
      }
    } catch (error) {
      console.error('Erreur géocodage:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {message && (
        <div className={`p-4 rounded-md ${
          message.includes('succès') ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
        }`}>
          {message}
        </div>
      )}

      {/* Activation de la vitrine */}
      <Card>
        <CardHeader>
          <CardTitle>Activation du site vitrine</CardTitle>
        </CardHeader>
        <CardContent>
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              name="isVitrineActive"
              checked={formData.isVitrineActive}
              onChange={handleChange}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-gray-700">
              Activer mon site vitrine (visible publiquement)
            </span>
          </label>
          <p className="text-sm text-gray-500 mt-2">
            Une fois activé, votre site vitrine sera accessible à l'adresse : 
            <code className="bg-gray-100 px-2 py-1 rounded ml-1">
              /vitrine/{vitrineData._id}
            </code>
          </p>
        </CardContent>
      </Card>

      {/* Description du commerce */}
      <Card>
        <CardHeader>
          <CardTitle>Description de votre commerce</CardTitle>
        </CardHeader>
        <CardContent>
          <textarea
            name="businessDescription"
            value={formData.businessDescription}
            onChange={handleChange}
            rows={6}
            placeholder="Décrivez votre commerce, votre histoire, vos valeurs..."
            className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
        </CardContent>
      </Card>

      {/* Catégorie et tags */}
      <Card>
        <CardHeader>
          <CardTitle>Catégorie et spécialités</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Catégorie de commerce
            </label>
            <input
              type="text"
              name="businessCategory"
              value={formData.businessCategory}
              onChange={handleChange}
              placeholder="Ex: Salon de coiffure, Restaurant, Boutique..."
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Spécialités (séparées par des virgules)
            </label>
            <input
              type="text"
              name="businessTags"
              value={formData.businessTags}
              onChange={handleChange}
              placeholder="Ex: Coupe homme, Coloration, Brushing..."
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
        </CardContent>
      </Card>

      {/* Réseaux sociaux */}
      <Card>
        <CardHeader>
          <CardTitle>Réseaux sociaux</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Facebook
              </label>
              <input
                type="url"
                name="socialMedia.facebook"
                value={formData.socialMedia.facebook}
                onChange={handleChange}
                placeholder="https://facebook.com/votre-page"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Instagram
              </label>
              <input
                type="url"
                name="socialMedia.instagram"
                value={formData.socialMedia.instagram}
                onChange={handleChange}
                placeholder="https://instagram.com/votre-compte"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Site web
              </label>
              <input
                type="url"
                name="socialMedia.website"
                value={formData.socialMedia.website}
                onChange={handleChange}
                placeholder="https://votre-site.com"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                LinkedIn
              </label>
              <input
                type="url"
                name="socialMedia.linkedin"
                value={formData.socialMedia.linkedin}
                onChange={handleChange}
                placeholder="https://linkedin.com/company/votre-entreprise"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Coordonnées GPS */}
      <Card>
        <CardHeader>
          <CardTitle>Localisation GPS</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-gray-600">
            Ajoutez vos coordonnées GPS pour afficher votre commerce sur Google Maps
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Latitude
              </label>
              <input
                type="number"
                step="any"
                name="coordinates.latitude"
                value={formData.coordinates.latitude}
                onChange={handleChange}
                placeholder="48.8566"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Longitude
              </label>
              <input
                type="number"
                step="any"
                name="coordinates.longitude"
                value={formData.coordinates.longitude}
                onChange={handleChange}
                placeholder="2.3522"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
          <button
            type="button"
            onClick={getAddressFromCoordinates}
            className="text-blue-600 hover:text-blue-800 text-sm"
          >
            Vérifier l'adresse avec ces coordonnées
          </button>
          <p className="text-xs text-gray-500">
            Astuce : Vous pouvez trouver vos coordonnées sur Google Maps en cliquant droit sur votre emplacement
          </p>
        </CardContent>
      </Card>

      {/* Bouton de sauvegarde */}
      <div className="flex justify-end">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Mise à jour...' : 'Mettre à jour la vitrine'}
        </Button>
      </div>
    </form>
  );
}
