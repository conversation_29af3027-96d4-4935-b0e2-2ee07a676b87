const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// Configuration de la base de données
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/rdv-app';

// Schémas simplifiés pour le seeding
const UserSchema = new mongoose.Schema({
  name: String,
  email: String,
  password: String,
  businessName: String,
  phone: String,
  address: String,
  services: [String],
  isAdmin: { type: Boolean, default: false },
  isVerified: { type: Boolean, default: false },
  subscriptionStatus: { type: String, default: 'active' },
  businessDescription: String,
  businessImages: [String],
  socialMedia: {
    facebook: String,
    instagram: String,
    twitter: String,
    linkedin: String,
    website: String,
  },
  coordinates: {
    latitude: Number,
    longitude: Number,
  },
  businessCategory: String,
  businessTags: [String],
  isVitrineActive: { type: Boolean, default: false },
  schedule: {
    type: Map,
    of: {
      start: String,
      end: String,
      isOpen: Boolean,
    },
  },
}, { timestamps: true });

const BookingSchema = new mongoose.Schema({
  clientId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  businessId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  service: String,
  date: Date,
  time: String,
  status: { type: String, enum: ['pending', 'confirmed', 'cancelled', 'completed'], default: 'pending' },
  notes: String,
  duration: { type: Number, default: 60 },
  price: { type: Number, default: 0 },
}, { timestamps: true });

const PaymentSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  amount: Number,
  currency: { type: String, default: 'EUR' },
  status: { type: String, enum: ['pending', 'completed', 'failed'], default: 'pending' },
  paymentMethod: String,
  subscriptionPlan: String,
  subscriptionPeriod: String,
  startDate: Date,
  endDate: Date,
}, { timestamps: true });

// Données de test
const testUsers = [
  {
    name: "Sophie Martin",
    email: "<EMAIL>",
    password: "password123",
    businessName: "Coiffure Élégance",
    phone: "01 42 34 56 78",
    address: "15 Rue de la Paix, 75001 Paris",
    services: ["Coupe femme", "Coupe homme", "Coloration", "Brushing", "Permanente"],
    isVerified: true,
    businessDescription: "Salon de coiffure moderne au cœur de Paris. Nous proposons des coupes tendances et des colorations personnalisées dans une ambiance chaleureuse et professionnelle.",
    businessImages: [
      "https://images.unsplash.com/photo-1560066984-138dadb4c035?w=800",
      "https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=800"
    ],
    socialMedia: {
      facebook: "https://facebook.com/coiffure-elegance",
      instagram: "https://instagram.com/coiffure_elegance",
      website: "https://coiffure-elegance.fr"
    },
    coordinates: { latitude: 48.8566, longitude: 2.3522 },
    businessCategory: "Salon de coiffure",
    businessTags: ["Coiffure", "Coloration", "Soins capillaires", "Tendance"],
    isVitrineActive: true,
    schedule: {
      monday: { start: "09:00", end: "19:00", isOpen: true },
      tuesday: { start: "09:00", end: "19:00", isOpen: true },
      wednesday: { start: "09:00", end: "19:00", isOpen: true },
      thursday: { start: "09:00", end: "19:00", isOpen: true },
      friday: { start: "09:00", end: "19:00", isOpen: true },
      saturday: { start: "09:00", end: "18:00", isOpen: true },
      sunday: { start: "10:00", end: "16:00", isOpen: false }
    }
  },
  {
    name: "Marc Dubois",
    email: "<EMAIL>",
    password: "password123",
    businessName: "Restaurant Les Saveurs",
    phone: "01 45 67 89 12",
    address: "28 Avenue des Champs-Élysées, 75008 Paris",
    services: ["Déjeuner", "Dîner", "Brunch", "Événements privés", "Traiteur"],
    isVerified: true,
    businessDescription: "Restaurant gastronomique proposant une cuisine française raffinée avec des produits de saison. Ambiance élégante pour vos repas d'affaires et occasions spéciales.",
    businessImages: [
      "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=800",
      "https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=800"
    ],
    socialMedia: {
      facebook: "https://facebook.com/restaurant-saveurs",
      instagram: "https://instagram.com/les_saveurs_paris",
      website: "https://restaurant-saveurs.fr"
    },
    coordinates: { latitude: 48.8698, longitude: 2.3076 },
    businessCategory: "Restaurant",
    businessTags: ["Gastronomie", "Cuisine française", "Produits de saison", "Élégant"],
    isVitrineActive: true,
    schedule: {
      monday: { start: "12:00", end: "14:30", isOpen: true },
      tuesday: { start: "12:00", end: "14:30", isOpen: true },
      wednesday: { start: "12:00", end: "14:30", isOpen: true },
      thursday: { start: "12:00", end: "14:30", isOpen: true },
      friday: { start: "12:00", end: "14:30", isOpen: true },
      saturday: { start: "12:00", end: "14:30", isOpen: true },
      sunday: { start: "12:00", end: "14:30", isOpen: false }
    }
  },
  {
    name: "Julie Moreau",
    email: "<EMAIL>",
    password: "password123",
    businessName: "Beauté & Wellness",
    phone: "01 56 78 90 23",
    address: "42 Rue du Faubourg Saint-Honoré, 75008 Paris",
    services: ["Soin du visage", "Massage", "Manucure", "Pédicure", "Épilation"],
    isVerified: true,
    businessDescription: "Institut de beauté et de bien-être offrant des soins personnalisés dans un cadre relaxant. Nos esthéticiennes diplômées vous accueillent pour des moments de détente absolue.",
    businessImages: [
      "https://images.unsplash.com/photo-1540555700478-4be289fbecef?w=800",
      "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800"
    ],
    socialMedia: {
      facebook: "https://facebook.com/beaute-wellness",
      instagram: "https://instagram.com/beaute_wellness_paris"
    },
    coordinates: { latitude: 48.8704, longitude: 2.3167 },
    businessCategory: "Institut de beauté",
    businessTags: ["Beauté", "Bien-être", "Relaxation", "Soins"],
    isVitrineActive: true,
    schedule: {
      monday: { start: "10:00", end: "19:00", isOpen: true },
      tuesday: { start: "10:00", end: "19:00", isOpen: true },
      wednesday: { start: "10:00", end: "19:00", isOpen: true },
      thursday: { start: "10:00", end: "19:00", isOpen: true },
      friday: { start: "10:00", end: "19:00", isOpen: true },
      saturday: { start: "09:00", end: "18:00", isOpen: true },
      sunday: { start: "10:00", end: "17:00", isOpen: false }
    }
  },
  {
    name: "Pierre Leroy",
    email: "<EMAIL>",
    password: "password123",
    businessName: "Garage Expert Auto",
    phone: "01 67 89 01 34",
    address: "156 Boulevard Périphérique, 75015 Paris",
    services: ["Révision", "Réparation", "Pneus", "Climatisation", "Contrôle technique"],
    isVerified: true,
    businessDescription: "Garage automobile spécialisé dans l'entretien et la réparation de tous types de véhicules. Équipe de mécaniciens expérimentés et équipements de pointe.",
    businessImages: [
      "https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=800"
    ],
    socialMedia: {
      website: "https://garage-expert.fr"
    },
    coordinates: { latitude: 48.8422, longitude: 2.2969 },
    businessCategory: "Garage automobile",
    businessTags: ["Automobile", "Réparation", "Entretien", "Mécanique"],
    isVitrineActive: true,
    schedule: {
      monday: { start: "08:00", end: "18:00", isOpen: true },
      tuesday: { start: "08:00", end: "18:00", isOpen: true },
      wednesday: { start: "08:00", end: "18:00", isOpen: true },
      thursday: { start: "08:00", end: "18:00", isOpen: true },
      friday: { start: "08:00", end: "18:00", isOpen: true },
      saturday: { start: "08:00", end: "12:00", isOpen: true },
      sunday: { start: "08:00", end: "12:00", isOpen: false }
    }
  },
  {
    name: "Client Test",
    email: "<EMAIL>",
    password: "password123",
    businessName: "Client Personnel",
    phone: "01 23 45 67 89",
    address: "123 Rue de Test, 75001 Paris",
    services: [],
    isVerified: true,
    isVitrineActive: false
  }
];

async function seedDatabase() {
  try {
    console.log('🔄 Connexion à MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connecté à MongoDB');

    // Créer les modèles
    const User = mongoose.model('User', UserSchema);
    const Booking = mongoose.model('Booking', BookingSchema);
    const Payment = mongoose.model('Payment', PaymentSchema);

    // Nettoyer la base de données
    console.log('🧹 Nettoyage de la base de données...');
    await User.deleteMany({});
    await Booking.deleteMany({});
    await Payment.deleteMany({});

    // Créer les utilisateurs
    console.log('👥 Création des utilisateurs...');
    const createdUsers = [];
    
    for (const userData of testUsers) {
      const hashedPassword = await bcrypt.hash(userData.password, 10);
      const user = new User({
        ...userData,
        password: hashedPassword
      });
      const savedUser = await user.save();
      createdUsers.push(savedUser);
      console.log(`✅ Utilisateur créé: ${userData.name} (${userData.email})`);
    }

    // Créer l'admin
    console.log('👑 Création de l\'administrateur...');
    const adminPassword = await bcrypt.hash('admin123', 10);
    const admin = new User({
      name: "Administrateur",
      email: "<EMAIL>",
      password: adminPassword,
      businessName: "Administration RDV",
      isAdmin: true,
      isVerified: true,
      subscriptionStatus: 'active'
    });
    const savedAdmin = await admin.save();
    console.log('✅ Administrateur créé: <EMAIL> / admin123');

    // Créer des réservations de test
    console.log('📅 Création des réservations...');
    const businesses = createdUsers.filter(user => user.services.length > 0);
    const client = createdUsers.find(user => user.email === '<EMAIL>');
    
    const bookings = [];
    for (let i = 0; i < 15; i++) {
      const business = businesses[Math.floor(Math.random() * businesses.length)];
      const service = business.services[Math.floor(Math.random() * business.services.length)];
      const date = new Date();
      date.setDate(date.getDate() + Math.floor(Math.random() * 30) - 15); // ±15 jours
      
      const statuses = ['pending', 'confirmed', 'cancelled', 'completed'];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      
      const booking = new Booking({
        clientId: client._id,
        businessId: business._id,
        service: service,
        date: date,
        time: `${9 + Math.floor(Math.random() * 8)}:${Math.random() > 0.5 ? '00' : '30'}`,
        status: status,
        notes: `Réservation de test pour ${service}`,
        duration: 60 + Math.floor(Math.random() * 60),
        price: 30 + Math.floor(Math.random() * 100)
      });
      
      const savedBooking = await booking.save();
      bookings.push(savedBooking);
    }
    console.log(`✅ ${bookings.length} réservations créées`);

    // Créer des paiements de test
    console.log('💳 Création des paiements...');
    const payments = [];
    for (const business of businesses) {
      for (let i = 0; i < 3; i++) {
        const plans = ['monthly', 'yearly'];
        const plan = plans[Math.floor(Math.random() * plans.length)];
        const amount = plan === 'monthly' ? 29 : 290;
        
        const statuses = ['pending', 'completed', 'failed'];
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - Math.floor(Math.random() * 6));
        
        const endDate = new Date(startDate);
        endDate.setMonth(endDate.getMonth() + (plan === 'monthly' ? 1 : 12));
        
        const payment = new Payment({
          userId: business._id,
          amount: amount,
          currency: 'EUR',
          status: status,
          paymentMethod: 'card',
          subscriptionPlan: 'premium',
          subscriptionPeriod: plan,
          startDate: startDate,
          endDate: endDate
        });
        
        const savedPayment = await payment.save();
        payments.push(savedPayment);
      }
    }
    console.log(`✅ ${payments.length} paiements créés`);

    console.log('\n🎉 Base de données initialisée avec succès !');
    console.log('\n📋 Comptes de test créés :');
    console.log('👑 Admin: <EMAIL> / admin123');
    console.log('👤 Client: <EMAIL> / password123');
    console.log('\n🏪 Commerces avec vitrines actives :');
    businesses.forEach(business => {
      console.log(`   • ${business.businessName}: ${business.email} / password123`);
    });
    
    console.log('\n🔗 URLs à tester :');
    console.log('   • Page d\'accueil: http://localhost:3000');
    console.log('   • Découvrir les commerces: http://localhost:3000/vitrines');
    console.log('   • Admin: http://localhost:3000/admin');
    console.log('   • Dashboard: http://localhost:3000/dashboard');
    
    businesses.forEach(business => {
      console.log(`   • Vitrine ${business.businessName}: http://localhost:3000/vitrine/${business._id}`);
    });

  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Déconnecté de MongoDB');
  }
}

// Exécuter le script
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase };
