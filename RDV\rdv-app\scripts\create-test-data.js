// Script simple pour créer des données de test
console.log('🚀 Création des données de test...');

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// Connexion simple
mongoose.connect('mongodb://localhost:27017/rdv-app')
  .then(() => {
    console.log('✅ Connecté à MongoDB');
    return createData();
  })
  .then(() => {
    console.log('🎉 Données créées avec succès !');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erreur:', error);
    process.exit(1);
  });

async function createData() {
  // Schémas simples
  const User = mongoose.model('User', new mongoose.Schema({
    name: String,
    email: { type: String, unique: true },
    password: String,
    businessName: String,
    phone: String,
    address: String,
    services: [String],
    isAdmin: { type: Boolean, default: false },
    isVerified: { type: Boolean, default: false },
    subscriptionStatus: { type: String, default: 'active' },
    businessDescription: String,
    businessCategory: String,
    businessTags: [String],
    isVitrineActive: { type: Boolean, default: false },
    socialMedia: {
      facebook: String,
      instagram: String,
      website: String,
    },
    coordinates: {
      latitude: Number,
      longitude: Number,
    },
    schedule: mongoose.Schema.Types.Mixed,
  }, { timestamps: true }));

  const Booking = mongoose.model('Booking', new mongoose.Schema({
    clientId: mongoose.Schema.Types.ObjectId,
    businessId: mongoose.Schema.Types.ObjectId,
    service: String,
    date: Date,
    time: String,
    status: { type: String, default: 'pending' },
    notes: String,
    price: Number,
  }, { timestamps: true }));

  const Payment = mongoose.model('Payment', new mongoose.Schema({
    userId: mongoose.Schema.Types.ObjectId,
    amount: Number,
    currency: { type: String, default: 'EUR' },
    status: { type: String, default: 'completed' },
    paymentMethod: String,
    subscriptionPlan: String,
    subscriptionPeriod: String,
    startDate: Date,
    endDate: Date,
  }, { timestamps: true }));

  // Supprimer les données existantes
  console.log('🧹 Suppression des données existantes...');
  await User.deleteMany({});
  await Booking.deleteMany({});
  await Payment.deleteMany({});

  // Créer l'admin
  console.log('👑 Création de l\'admin...');
  const adminPassword = await bcrypt.hash('admin123', 10);
  const admin = await User.create({
    name: "Admin",
    email: "<EMAIL>",
    password: adminPassword,
    businessName: "Administration",
    isAdmin: true,
    isVerified: true,
  });

  // Créer les commerces
  console.log('🏪 Création des commerces...');
  const password = await bcrypt.hash('password123', 10);
  
  const coiffeur = await User.create({
    name: "Sophie Martin",
    email: "<EMAIL>",
    password: password,
    businessName: "Salon Sophie",
    phone: "01 42 34 56 78",
    address: "15 Rue de la Paix, 75001 Paris",
    services: ["Coupe femme", "Coupe homme", "Coloration", "Brushing"],
    isVerified: true,
    businessDescription: "Salon de coiffure moderne au cœur de Paris. Nous proposons des coupes tendances et des colorations personnalisées.",
    businessCategory: "Salon de coiffure",
    businessTags: ["Coiffure", "Coloration", "Tendance"],
    isVitrineActive: true,
    coordinates: { latitude: 48.8566, longitude: 2.3522 },
    socialMedia: {
      facebook: "https://facebook.com/salon-sophie",
      instagram: "https://instagram.com/salon_sophie"
    },
    schedule: {
      monday: { start: "09:00", end: "19:00", isOpen: true },
      tuesday: { start: "09:00", end: "19:00", isOpen: true },
      wednesday: { start: "09:00", end: "19:00", isOpen: true },
      thursday: { start: "09:00", end: "19:00", isOpen: true },
      friday: { start: "09:00", end: "19:00", isOpen: true },
      saturday: { start: "09:00", end: "18:00", isOpen: true },
      sunday: { start: "10:00", end: "16:00", isOpen: false }
    }
  });

  const restaurant = await User.create({
    name: "Marc Dubois",
    email: "<EMAIL>",
    password: password,
    businessName: "Restaurant Les Saveurs",
    phone: "01 45 67 89 12",
    address: "28 Avenue des Champs-Élysées, 75008 Paris",
    services: ["Déjeuner", "Dîner", "Brunch", "Événements privés"],
    isVerified: true,
    businessDescription: "Restaurant gastronomique proposant une cuisine française raffinée avec des produits de saison.",
    businessCategory: "Restaurant",
    businessTags: ["Gastronomie", "Cuisine française", "Élégant"],
    isVitrineActive: true,
    coordinates: { latitude: 48.8698, longitude: 2.3076 },
    socialMedia: {
      website: "https://restaurant-saveurs.fr"
    },
    schedule: {
      monday: { start: "12:00", end: "22:00", isOpen: true },
      tuesday: { start: "12:00", end: "22:00", isOpen: true },
      wednesday: { start: "12:00", end: "22:00", isOpen: true },
      thursday: { start: "12:00", end: "22:00", isOpen: true },
      friday: { start: "12:00", end: "22:00", isOpen: true },
      saturday: { start: "12:00", end: "22:00", isOpen: true },
      sunday: { start: "12:00", end: "22:00", isOpen: false }
    }
  });

  const beaute = await User.create({
    name: "Julie Moreau",
    email: "<EMAIL>",
    password: password,
    businessName: "Beauté & Wellness",
    phone: "01 56 78 90 23",
    address: "42 Rue du Faubourg Saint-Honoré, 75008 Paris",
    services: ["Soin du visage", "Massage", "Manucure", "Pédicure"],
    isVerified: true,
    businessDescription: "Institut de beauté et de bien-être offrant des soins personnalisés dans un cadre relaxant.",
    businessCategory: "Institut de beauté",
    businessTags: ["Beauté", "Bien-être", "Relaxation"],
    isVitrineActive: true,
    coordinates: { latitude: 48.8704, longitude: 2.3167 },
    schedule: {
      monday: { start: "10:00", end: "19:00", isOpen: true },
      tuesday: { start: "10:00", end: "19:00", isOpen: true },
      wednesday: { start: "10:00", end: "19:00", isOpen: true },
      thursday: { start: "10:00", end: "19:00", isOpen: true },
      friday: { start: "10:00", end: "19:00", isOpen: true },
      saturday: { start: "09:00", end: "18:00", isOpen: true },
      sunday: { start: "10:00", end: "17:00", isOpen: false }
    }
  });

  const client = await User.create({
    name: "Client Test",
    email: "<EMAIL>",
    password: password,
    businessName: "Client",
    phone: "01 23 45 67 89",
    isVerified: true
  });

  // Créer des réservations
  console.log('📅 Création des réservations...');
  const today = new Date();
  
  await Booking.create([
    {
      clientId: client._id,
      businessId: coiffeur._id,
      service: "Coupe femme",
      date: new Date(today.getTime() + 1 * 24 * 60 * 60 * 1000),
      time: "14:00",
      status: "confirmed",
      notes: "Première visite",
      price: 45
    },
    {
      clientId: client._id,
      businessId: restaurant._id,
      service: "Dîner",
      date: new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000),
      time: "20:00",
      status: "pending",
      notes: "Table pour 2 personnes",
      price: 80
    },
    {
      clientId: client._id,
      businessId: beaute._id,
      service: "Soin du visage",
      date: new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000),
      time: "15:30",
      status: "confirmed",
      price: 65
    }
  ]);

  // Créer des paiements
  console.log('💳 Création des paiements...');
  const startDate = new Date();
  const endDate = new Date();
  endDate.setMonth(endDate.getMonth() + 1);

  await Payment.create([
    {
      userId: coiffeur._id,
      amount: 29,
      status: "completed",
      paymentMethod: "card",
      subscriptionPlan: "premium",
      subscriptionPeriod: "monthly",
      startDate: startDate,
      endDate: endDate
    },
    {
      userId: restaurant._id,
      amount: 290,
      status: "completed",
      paymentMethod: "card",
      subscriptionPlan: "premium",
      subscriptionPeriod: "yearly",
      startDate: startDate,
      endDate: new Date(startDate.getTime() + 365 * 24 * 60 * 60 * 1000)
    },
    {
      userId: beaute._id,
      amount: 29,
      status: "pending",
      paymentMethod: "card",
      subscriptionPlan: "premium",
      subscriptionPeriod: "monthly",
      startDate: startDate,
      endDate: endDate
    }
  ]);

  console.log('\n📋 Comptes créés :');
  console.log('👑 Admin: <EMAIL> / admin123');
  console.log('👤 Client: <EMAIL> / password123');
  console.log('🏪 Coiffeur: <EMAIL> / password123');
  console.log('🍽️ Restaurant: <EMAIL> / password123');
  console.log('💅 Beauté: <EMAIL> / password123');
  
  console.log('\n🔗 URLs à tester :');
  console.log(`   • Vitrine coiffeur: http://localhost:3000/vitrine/${coiffeur._id}`);
  console.log(`   • Vitrine restaurant: http://localhost:3000/vitrine/${restaurant._id}`);
  console.log(`   • Vitrine beauté: http://localhost:3000/vitrine/${beaute._id}`);
  console.log('   • Toutes les vitrines: http://localhost:3000/vitrines');
  console.log('   • Admin: http://localhost:3000/admin');
}
