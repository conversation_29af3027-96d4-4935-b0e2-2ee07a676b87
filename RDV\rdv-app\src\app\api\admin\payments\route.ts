import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import Payment from '@/models/Payment';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: 'Non autorisé' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);

    // Récupérer les paramètres de filtre
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const minAmount = searchParams.get('minAmount');
    const maxAmount = searchParams.get('maxAmount');

    await connectDB();

    // Construire la requête de filtre
    const filter: any = {};

    if (status && status !== '') {
      filter.status = status;
    }

    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) {
        filter.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        filter.createdAt.$lte = endDateTime;
      }
    }

    if (minAmount || maxAmount) {
      filter.amount = {};
      if (minAmount) {
        filter.amount.$gte = parseFloat(minAmount);
      }
      if (maxAmount) {
        filter.amount.$lte = parseFloat(maxAmount);
      }
    }

    const payments = await Payment.find(filter)
      .populate('userId', 'name email businessName')
      .sort({ createdAt: -1 });

    const serializedPayments = payments.map(payment => ({
      _id: payment._id.toString(),
      userId: {
        name: payment.userId?.name || 'Utilisateur supprimé',
        email: payment.userId?.email || '',
        businessName: payment.userId?.businessName || 'Commerce supprimé',
      },
      amount: payment.amount,
      currency: payment.currency,
      status: payment.status,
      paymentMethod: payment.paymentMethod,
      subscriptionPlan: payment.subscriptionPlan,
      subscriptionPeriod: payment.subscriptionPeriod,
      startDate: payment.startDate.toISOString(),
      endDate: payment.endDate.toISOString(),
      createdAt: payment.createdAt.toISOString(),
    }));

    return NextResponse.json({
      payments: serializedPayments,
      total: payments.length
    });

  } catch (error: any) {
    console.error('Erreur lors de la récupération des paiements:', error);
    return NextResponse.json(
      { message: 'Erreur serveur', error: error.message },
      { status: 500 }
    );
  }
}