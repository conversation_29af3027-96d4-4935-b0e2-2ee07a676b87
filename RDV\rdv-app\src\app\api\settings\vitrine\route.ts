import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';

export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { message: 'Non autorisé' },
        { status: 401 }
      );
    }

    const {
      businessDescription,
      businessCategory,
      businessTags,
      socialMedia,
      coordinates,
      isVitrineActive,
    } = await request.json();

    await connectDB();

    const updatedUser = await User.findByIdAndUpdate(
      session.user.id,
      {
        businessDescription: businessDescription || '',
        businessCategory: businessCategory || '',
        businessTags: Array.isArray(businessTags) ? businessTags : [],
        socialMedia: {
          facebook: socialMedia?.facebook || '',
          instagram: socialMedia?.instagram || '',
          twitter: socialMedia?.twitter || '',
          linkedin: socialMedia?.linkedin || '',
          website: socialMedia?.website || '',
        },
        coordinates: {
          latitude: coordinates?.latitude || 0,
          longitude: coordinates?.longitude || 0,
        },
        isVitrineActive: Boolean(isVitrineActive),
      },
      { new: true }
    ).select('businessName businessDescription businessCategory businessTags socialMedia coordinates isVitrineActive');

    if (!updatedUser) {
      return NextResponse.json(
        { message: 'Utilisateur non trouvé' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Site vitrine mis à jour avec succès',
      vitrine: {
        _id: updatedUser._id.toString(),
        businessName: updatedUser.businessName,
        businessDescription: updatedUser.businessDescription,
        businessCategory: updatedUser.businessCategory,
        businessTags: updatedUser.businessTags,
        socialMedia: updatedUser.socialMedia,
        coordinates: updatedUser.coordinates,
        isVitrineActive: updatedUser.isVitrineActive,
      }
    });
  } catch (error: any) {
    console.error('Erreur lors de la mise à jour de la vitrine:', error);
    return NextResponse.json(
      { message: 'Erreur serveur', error: error.message },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { message: 'Non autorisé' },
        { status: 401 }
      );
    }

    await connectDB();

    const user = await User.findById(session.user.id).select(
      'businessName businessDescription businessCategory businessTags socialMedia coordinates isVitrineActive businessImages phone address services schedule'
    );

    if (!user) {
      return NextResponse.json(
        { message: 'Utilisateur non trouvé' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      vitrine: {
        _id: user._id.toString(),
        businessName: user.businessName,
        businessDescription: user.businessDescription || '',
        businessCategory: user.businessCategory || '',
        businessTags: user.businessTags || [],
        socialMedia: user.socialMedia || {},
        coordinates: user.coordinates || { latitude: 0, longitude: 0 },
        isVitrineActive: user.isVitrineActive || false,
        businessImages: user.businessImages || [],
        phone: user.phone || '',
        address: user.address || '',
        services: user.services || [],
        schedule: user.schedule || {},
      }
    });
  } catch (error: any) {
    console.error('Erreur lors de la récupération de la vitrine:', error);
    return NextResponse.json(
      { message: 'Erreur serveur', error: error.message },
      { status: 500 }
    );
  }
}
