'use client';

import { useState, useEffect, useRef } from 'react';
import Button from '@/components/ui/Button';

interface TextToSpeechProps {
  text: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  autoPlay?: boolean;
}

export default function TextToSpeech({ 
  text, 
  className = '', 
  size = 'sm',
  showLabel = false,
  autoPlay = false 
}: TextToSpeechProps) {
  const [isSupported, setIsSupported] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);

  useEffect(() => {
    // Vérifier si l'API Speech Synthesis est supportée
    setIsSupported('speechSynthesis' in window);

    // Auto-play si demandé
    if (autoPlay && 'speechSynthesis' in window) {
      handlePlay();
    }

    return () => {
      // Nettoyer lors du démontage
      if (utteranceRef.current) {
        window.speechSynthesis.cancel();
      }
    };
  }, [autoPlay]);

  const handlePlay = () => {
    if (!isSupported || !text.trim()) return;

    // Si en pause, reprendre
    if (isPaused) {
      window.speechSynthesis.resume();
      setIsPaused(false);
      setIsPlaying(true);
      return;
    }

    // Arrêter toute lecture en cours
    window.speechSynthesis.cancel();

    // Créer une nouvelle utterance
    const utterance = new SpeechSynthesisUtterance(text);
    utteranceRef.current = utterance;

    // Configuration de la voix
    utterance.rate = 0.9; // Vitesse légèrement réduite pour la clarté
    utterance.pitch = 1;
    utterance.volume = 1;

    // Essayer de trouver une voix française
    const voices = window.speechSynthesis.getVoices();
    const frenchVoice = voices.find(voice => 
      voice.lang.startsWith('fr') || voice.name.toLowerCase().includes('french')
    );
    if (frenchVoice) {
      utterance.voice = frenchVoice;
    }

    // Événements
    utterance.onstart = () => {
      setIsPlaying(true);
      setIsPaused(false);
    };

    utterance.onend = () => {
      setIsPlaying(false);
      setIsPaused(false);
    };

    utterance.onerror = () => {
      setIsPlaying(false);
      setIsPaused(false);
      console.error('Erreur lors de la lecture vocale');
    };

    utterance.onpause = () => {
      setIsPaused(true);
      setIsPlaying(false);
    };

    // Démarrer la lecture
    window.speechSynthesis.speak(utterance);
  };

  const handlePause = () => {
    if (isPlaying) {
      window.speechSynthesis.pause();
      setIsPaused(true);
      setIsPlaying(false);
    }
  };

  const handleStop = () => {
    window.speechSynthesis.cancel();
    setIsPlaying(false);
    setIsPaused(false);
  };

  if (!isSupported) {
    return null; // Ne pas afficher le bouton si non supporté
  }

  const getButtonContent = () => {
    if (isPlaying) {
      return (
        <>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6" />
          </svg>
          {showLabel && <span className="ml-2">Pause</span>}
        </>
      );
    }

    if (isPaused) {
      return (
        <>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4h10a2 2 0 012 2v4a2 2 0 01-2 2H7a2 2 0 01-2-2v-4a2 2 0 012-2z" />
          </svg>
          {showLabel && <span className="ml-2">Reprendre</span>}
        </>
      );
    }

    return (
      <>
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 14.142M8.586 8.586A2 2 0 018 10v4a2 2 0 01.586 1.414L12 19l3.414-3.586A2 2 0 0116 14v-4a2 2 0 01-.586-1.414L12 5 8.586 8.586z" />
        </svg>
        {showLabel && <span className="ml-2">Lire</span>}
      </>
    );
  };

  const getAriaLabel = () => {
    if (isPlaying) return 'Mettre en pause la lecture vocale';
    if (isPaused) return 'Reprendre la lecture vocale';
    return 'Lire le texte à voix haute';
  };

  return (
    <div className={`inline-flex items-center space-x-2 ${className}`}>
      <Button
        variant="outline"
        size={size}
        onClick={isPlaying ? handlePause : handlePlay}
        aria-label={getAriaLabel()}
        title={getAriaLabel()}
        className="flex items-center"
      >
        {getButtonContent()}
      </Button>

      {(isPlaying || isPaused) && (
        <Button
          variant="outline"
          size={size}
          onClick={handleStop}
          aria-label="Arrêter la lecture vocale"
          title="Arrêter la lecture vocale"
          className="flex items-center"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10h6v4H9z" />
          </svg>
          {showLabel && <span className="ml-2">Stop</span>}
        </Button>
      )}
    </div>
  );
}
