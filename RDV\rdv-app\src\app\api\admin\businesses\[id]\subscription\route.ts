import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';

export async function PATCH(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: 'Non autorisé' },
        { status: 401 }
      );
    }

    const { status } = await req.json();

    if (!status || !['active', 'expired'].includes(status)) {
      return NextResponse.json(
        { message: 'Statut invalide' },
        { status: 400 }
      );
    }

    const { id } = await params;
    await connectDB();

    const user = await User.findByIdAndUpdate(
      id,
      { subscriptionStatus: status },
      { new: true }
    );

    if (!user) {
      return NextResponse.json(
        { message: 'Commerce non trouvé' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { message: 'Statut de l\'abonnement mis à jour avec succès' },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('Erreur lors de la mise à jour de l\'abonnement:', error);
    return NextResponse.json(
      { message: 'Une erreur est survenue lors de la mise à jour de l\'abonnement' },
      { status: 500 }
    );
  }
} 