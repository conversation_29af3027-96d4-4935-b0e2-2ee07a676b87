/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_wenic_RDV_rdv_app_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\RDV\\\\rdv-app\\\\src\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_wenic_RDV_rdv_app_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBaUM7QUFDUTtBQUV6QyxNQUFNRSxVQUFVRixnREFBUUEsQ0FBQ0Msa0RBQVdBO0FBRU8iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcd2VuaWNcXFJEVlxccmR2LWFwcFxcc3JjXFxhcHBcXGFwaVxcYXV0aFxcWy4uLm5leHRhdXRoXVxccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE5leHRBdXRoIGZyb20gJ25leHQtYXV0aCc7XHJcbmltcG9ydCB7IGF1dGhPcHRpb25zIH0gZnJvbSAnQC9saWIvYXV0aCc7XHJcblxyXG5jb25zdCBoYW5kbGVyID0gTmV4dEF1dGgoYXV0aE9wdGlvbnMpO1xyXG5cclxuZXhwb3J0IHsgaGFuZGxlciBhcyBHRVQsIGhhbmRsZXIgYXMgUE9TVCB9OyAiXSwibmFtZXMiOlsiTmV4dEF1dGgiLCJhdXRoT3B0aW9ucyIsImhhbmRsZXIiLCJHRVQiLCJQT1NUIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'Credentials',\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Mot de passe\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    throw new Error('Email et mot de passe requis');\n                }\n                await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n                const user = await _models_User__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findOne({\n                    email: credentials.email\n                });\n                if (!user) {\n                    throw new Error('Aucun utilisateur trouvé avec cet email');\n                }\n                const isValid = await user.comparePassword(credentials.password);\n                if (!isValid) {\n                    throw new Error('Mot de passe incorrect');\n                }\n                return {\n                    id: user._id.toString(),\n                    email: user.email,\n                    name: user.name,\n                    businessName: user.businessName,\n                    isAdmin: user.isAdmin\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    pages: {\n        signIn: '/auth/signin'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.businessName = user.businessName;\n                token.isAdmin = user.isAdmin;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.businessName = token.businessName;\n                session.user.isAdmin = token.isAdmin;\n            }\n            return session;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUNrRTtBQUM1QjtBQUNMO0FBRTFCLE1BQU1HLGNBQStCO0lBQzFDQyxXQUFXO1FBQ1RKLDJFQUFtQkEsQ0FBQztZQUNsQkssTUFBTTtZQUNOQyxhQUFhO2dCQUNYQyxPQUFPO29CQUFFQyxPQUFPO29CQUFTQyxNQUFNO2dCQUFRO2dCQUN2Q0MsVUFBVTtvQkFBRUYsT0FBTztvQkFBZ0JDLE1BQU07Z0JBQVc7WUFDdEQ7WUFDQSxNQUFNRSxXQUFVTCxXQUFXO2dCQUN6QixJQUFJLENBQUNBLGFBQWFDLFNBQVMsQ0FBQ0QsYUFBYUksVUFBVTtvQkFDakQsTUFBTSxJQUFJRSxNQUFNO2dCQUNsQjtnQkFFQSxNQUFNWCx3REFBU0E7Z0JBRWYsTUFBTVksT0FBTyxNQUFNWCxvREFBSUEsQ0FBQ1ksT0FBTyxDQUFDO29CQUFFUCxPQUFPRCxZQUFZQyxLQUFLO2dCQUFDO2dCQUUzRCxJQUFJLENBQUNNLE1BQU07b0JBQ1QsTUFBTSxJQUFJRCxNQUFNO2dCQUNsQjtnQkFFQSxNQUFNRyxVQUFVLE1BQU1GLEtBQUtHLGVBQWUsQ0FBQ1YsWUFBWUksUUFBUTtnQkFFL0QsSUFBSSxDQUFDSyxTQUFTO29CQUNaLE1BQU0sSUFBSUgsTUFBTTtnQkFDbEI7Z0JBRUEsT0FBTztvQkFDTEssSUFBSUosS0FBS0ssR0FBRyxDQUFDQyxRQUFRO29CQUNyQlosT0FBT00sS0FBS04sS0FBSztvQkFDakJGLE1BQU1RLEtBQUtSLElBQUk7b0JBQ2ZlLGNBQWNQLEtBQUtPLFlBQVk7b0JBQy9CQyxTQUFTUixLQUFLUSxPQUFPO2dCQUN2QjtZQUNGO1FBQ0Y7S0FDRDtJQUNEQyxTQUFTO1FBQ1BDLFVBQVU7SUFDWjtJQUNBQyxPQUFPO1FBQ0xDLFFBQVE7SUFFVjtJQUNBQyxXQUFXO1FBQ1QsTUFBTUMsS0FBSSxFQUFFQyxLQUFLLEVBQUVmLElBQUksRUFBRTtZQUN2QixJQUFJQSxNQUFNO2dCQUNSZSxNQUFNWCxFQUFFLEdBQUdKLEtBQUtJLEVBQUU7Z0JBQ2xCVyxNQUFNUixZQUFZLEdBQUdQLEtBQUtPLFlBQVk7Z0JBQ3RDUSxNQUFNUCxPQUFPLEdBQUdSLEtBQUtRLE9BQU87WUFDOUI7WUFDQSxPQUFPTztRQUNUO1FBQ0EsTUFBTU4sU0FBUSxFQUFFQSxPQUFPLEVBQUVNLEtBQUssRUFBRTtZQUM5QixJQUFJQSxPQUFPO2dCQUNUTixRQUFRVCxJQUFJLENBQUNJLEVBQUUsR0FBR1csTUFBTVgsRUFBRTtnQkFDMUJLLFFBQVFULElBQUksQ0FBQ08sWUFBWSxHQUFHUSxNQUFNUixZQUFZO2dCQUM5Q0UsUUFBUVQsSUFBSSxDQUFDUSxPQUFPLEdBQUdPLE1BQU1QLE9BQU87WUFDdEM7WUFDQSxPQUFPQztRQUNUO0lBQ0Y7QUFDRixFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHdlbmljXFxSRFZcXHJkdi1hcHBcXHNyY1xcbGliXFxhdXRoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRBdXRoT3B0aW9ucyB9IGZyb20gJ25leHQtYXV0aCc7XG5pbXBvcnQgQ3JlZGVudGlhbHNQcm92aWRlciBmcm9tICduZXh0LWF1dGgvcHJvdmlkZXJzL2NyZWRlbnRpYWxzJztcbmltcG9ydCBjb25uZWN0REIgZnJvbSAnQC9saWIvbW9uZ29kYic7XG5pbXBvcnQgVXNlciBmcm9tICdAL21vZGVscy9Vc2VyJztcblxuZXhwb3J0IGNvbnN0IGF1dGhPcHRpb25zOiBOZXh0QXV0aE9wdGlvbnMgPSB7XG4gIHByb3ZpZGVyczogW1xuICAgIENyZWRlbnRpYWxzUHJvdmlkZXIoe1xuICAgICAgbmFtZTogJ0NyZWRlbnRpYWxzJyxcbiAgICAgIGNyZWRlbnRpYWxzOiB7XG4gICAgICAgIGVtYWlsOiB7IGxhYmVsOiBcIkVtYWlsXCIsIHR5cGU6IFwiZW1haWxcIiB9LFxuICAgICAgICBwYXNzd29yZDogeyBsYWJlbDogXCJNb3QgZGUgcGFzc2VcIiwgdHlwZTogXCJwYXNzd29yZFwiIH1cbiAgICAgIH0sXG4gICAgICBhc3luYyBhdXRob3JpemUoY3JlZGVudGlhbHMpIHtcbiAgICAgICAgaWYgKCFjcmVkZW50aWFscz8uZW1haWwgfHwgIWNyZWRlbnRpYWxzPy5wYXNzd29yZCkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRW1haWwgZXQgbW90IGRlIHBhc3NlIHJlcXVpcycpO1xuICAgICAgICB9XG5cbiAgICAgICAgYXdhaXQgY29ubmVjdERCKCk7XG5cbiAgICAgICAgY29uc3QgdXNlciA9IGF3YWl0IFVzZXIuZmluZE9uZSh7IGVtYWlsOiBjcmVkZW50aWFscy5lbWFpbCB9KTtcblxuICAgICAgICBpZiAoIXVzZXIpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1Y3VuIHV0aWxpc2F0ZXVyIHRyb3V2w6kgYXZlYyBjZXQgZW1haWwnKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGlzVmFsaWQgPSBhd2FpdCB1c2VyLmNvbXBhcmVQYXNzd29yZChjcmVkZW50aWFscy5wYXNzd29yZCk7XG5cbiAgICAgICAgaWYgKCFpc1ZhbGlkKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdNb3QgZGUgcGFzc2UgaW5jb3JyZWN0Jyk7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGlkOiB1c2VyLl9pZC50b1N0cmluZygpLFxuICAgICAgICAgIGVtYWlsOiB1c2VyLmVtYWlsLFxuICAgICAgICAgIG5hbWU6IHVzZXIubmFtZSxcbiAgICAgICAgICBidXNpbmVzc05hbWU6IHVzZXIuYnVzaW5lc3NOYW1lLFxuICAgICAgICAgIGlzQWRtaW46IHVzZXIuaXNBZG1pbixcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICB9KVxuICBdLFxuICBzZXNzaW9uOiB7XG4gICAgc3RyYXRlZ3k6ICdqd3QnLFxuICB9LFxuICBwYWdlczoge1xuICAgIHNpZ25JbjogJy9hdXRoL3NpZ25pbicsXG4gICAgLy8gc2lnblVwOiAnL2F1dGgvc2lnbnVwJywgLy8gQ2V0dGUgb3B0aW9uIG4nZXhpc3RlIHBhcyBkYW5zIE5leHRBdXRoXG4gIH0sXG4gIGNhbGxiYWNrczoge1xuICAgIGFzeW5jIGp3dCh7IHRva2VuLCB1c2VyIH0pIHtcbiAgICAgIGlmICh1c2VyKSB7XG4gICAgICAgIHRva2VuLmlkID0gdXNlci5pZDtcbiAgICAgICAgdG9rZW4uYnVzaW5lc3NOYW1lID0gdXNlci5idXNpbmVzc05hbWU7XG4gICAgICAgIHRva2VuLmlzQWRtaW4gPSB1c2VyLmlzQWRtaW47XG4gICAgICB9XG4gICAgICByZXR1cm4gdG9rZW47XG4gICAgfSxcbiAgICBhc3luYyBzZXNzaW9uKHsgc2Vzc2lvbiwgdG9rZW4gfSkge1xuICAgICAgaWYgKHRva2VuKSB7XG4gICAgICAgIHNlc3Npb24udXNlci5pZCA9IHRva2VuLmlkIGFzIHN0cmluZztcbiAgICAgICAgc2Vzc2lvbi51c2VyLmJ1c2luZXNzTmFtZSA9IHRva2VuLmJ1c2luZXNzTmFtZSBhcyBzdHJpbmc7XG4gICAgICAgIHNlc3Npb24udXNlci5pc0FkbWluID0gdG9rZW4uaXNBZG1pbiBhcyBib29sZWFuO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHNlc3Npb247XG4gICAgfSxcbiAgfSxcbn07XG4iXSwibmFtZXMiOlsiQ3JlZGVudGlhbHNQcm92aWRlciIsImNvbm5lY3REQiIsIlVzZXIiLCJhdXRoT3B0aW9ucyIsInByb3ZpZGVycyIsIm5hbWUiLCJjcmVkZW50aWFscyIsImVtYWlsIiwibGFiZWwiLCJ0eXBlIiwicGFzc3dvcmQiLCJhdXRob3JpemUiLCJFcnJvciIsInVzZXIiLCJmaW5kT25lIiwiaXNWYWxpZCIsImNvbXBhcmVQYXNzd29yZCIsImlkIiwiX2lkIiwidG9TdHJpbmciLCJidXNpbmVzc05hbWUiLCJpc0FkbWluIiwic2Vzc2lvbiIsInN0cmF0ZWd5IiwicGFnZXMiLCJzaWduSW4iLCJjYWxsYmFja3MiLCJqd3QiLCJ0b2tlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Veuillez définir l\\'URI MongoDB dans les variables d\\'environnement');\n}\nlet cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts);\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/User.ts":
/*!****************************!*\
  !*** ./src/models/User.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n\n\nconst userSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    name: {\n        type: String,\n        required: [\n            true,\n            'Le nom est requis'\n        ]\n    },\n    email: {\n        type: String,\n        required: [\n            true,\n            'L\\'email est requis'\n        ],\n        unique: true,\n        lowercase: true,\n        trim: true\n    },\n    password: {\n        type: String,\n        required: [\n            true,\n            'Le mot de passe est requis'\n        ],\n        minlength: [\n            6,\n            'Le mot de passe doit contenir au moins 6 caractères'\n        ]\n    },\n    businessName: {\n        type: String,\n        required: [\n            true,\n            'Le nom du commerce est requis'\n        ]\n    },\n    phone: {\n        type: String,\n        default: ''\n    },\n    address: {\n        type: String,\n        default: ''\n    },\n    services: {\n        type: [\n            String\n        ],\n        default: []\n    },\n    // Champs pour le site vitrine\n    businessDescription: {\n        type: String,\n        default: ''\n    },\n    businessImages: {\n        type: [\n            String\n        ],\n        default: []\n    },\n    socialMedia: {\n        facebook: {\n            type: String,\n            default: ''\n        },\n        instagram: {\n            type: String,\n            default: ''\n        },\n        twitter: {\n            type: String,\n            default: ''\n        },\n        linkedin: {\n            type: String,\n            default: ''\n        },\n        website: {\n            type: String,\n            default: ''\n        }\n    },\n    coordinates: {\n        latitude: {\n            type: Number,\n            default: 0\n        },\n        longitude: {\n            type: Number,\n            default: 0\n        }\n    },\n    businessCategory: {\n        type: String,\n        default: ''\n    },\n    businessTags: {\n        type: [\n            String\n        ],\n        default: []\n    },\n    isVitrineActive: {\n        type: Boolean,\n        default: false\n    },\n    isAdmin: {\n        type: Boolean,\n        default: false\n    },\n    isVerified: {\n        type: Boolean,\n        default: false\n    },\n    subscriptionStatus: {\n        type: String,\n        enum: [\n            'active',\n            'expired',\n            'pending',\n            'trial',\n            'cancelled'\n        ],\n        default: 'trial'\n    },\n    subscriptionPlan: {\n        type: String,\n        enum: [\n            'freemium',\n            'premium',\n            'enterprise'\n        ],\n        default: 'freemium'\n    },\n    trialStartDate: {\n        type: Date,\n        default: Date.now\n    },\n    trialEndDate: {\n        type: Date,\n        default: function() {\n            const date = new Date();\n            date.setMonth(date.getMonth() + 1); // 1 mois d'essai\n            return date;\n        }\n    },\n    subscriptionStartDate: {\n        type: Date\n    },\n    subscriptionEndDate: {\n        type: Date\n    },\n    maxBookingsPerMonth: {\n        type: Number,\n        default: 10\n    },\n    maxServices: {\n        type: Number,\n        default: 3\n    },\n    workingHours: {\n        type: Map,\n        of: {\n            start: String,\n            end: String,\n            isOpen: Boolean\n        },\n        default: {\n            monday: {\n                start: '09:00',\n                end: '18:00',\n                isOpen: true\n            },\n            tuesday: {\n                start: '09:00',\n                end: '18:00',\n                isOpen: true\n            },\n            wednesday: {\n                start: '09:00',\n                end: '18:00',\n                isOpen: true\n            },\n            thursday: {\n                start: '09:00',\n                end: '18:00',\n                isOpen: true\n            },\n            friday: {\n                start: '09:00',\n                end: '18:00',\n                isOpen: true\n            },\n            saturday: {\n                start: '09:00',\n                end: '18:00',\n                isOpen: false\n            },\n            sunday: {\n                start: '09:00',\n                end: '18:00',\n                isOpen: false\n            }\n        }\n    },\n    schedule: {\n        type: Map,\n        of: {\n            start: String,\n            end: String,\n            isOpen: Boolean\n        },\n        default: {\n            monday: {\n                start: '09:00',\n                end: '18:00',\n                isOpen: true\n            },\n            tuesday: {\n                start: '09:00',\n                end: '18:00',\n                isOpen: true\n            },\n            wednesday: {\n                start: '09:00',\n                end: '18:00',\n                isOpen: true\n            },\n            thursday: {\n                start: '09:00',\n                end: '18:00',\n                isOpen: true\n            },\n            friday: {\n                start: '09:00',\n                end: '18:00',\n                isOpen: true\n            },\n            saturday: {\n                start: '09:00',\n                end: '18:00',\n                isOpen: true\n            },\n            sunday: {\n                start: '09:00',\n                end: '18:00',\n                isOpen: false\n            }\n        }\n    }\n}, {\n    timestamps: true\n});\n// Hash password before saving\nuserSchema.pre('save', async function(next) {\n    if (!this.isModified('password')) return next();\n    try {\n        const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].genSalt(10);\n        this.password = await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hash(this.password, salt);\n        next();\n    } catch (error) {\n        next(error);\n    }\n});\n// Compare password method\nuserSchema.methods.comparePassword = async function(candidatePassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].compare(candidatePassword, this.password);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('User', userSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/next-auth","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/@babel","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();