'use client';

import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';

export default function ConfirmationPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const [businessInfo, setBusinessInfo] = useState<any>(null);

  useEffect(() => {
    const fetchBusinessInfo = async () => {
      try {
        const response = await fetch(`/api/business/${params.userId}`);
        if (response.ok) {
          const data = await response.json();
          setBusinessInfo(data);
        }
      } catch (error) {
        console.error('Erreur lors du chargement des informations:', error);
      }
    };

    fetchBusinessInfo();
  }, [params.userId]);

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
              <svg
                className="h-6 w-6 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              Réservation confirmée !
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center">
              <p className="text-gray-600">
                Votre demande de rendez-vous a été envoyée avec succès.
              </p>
              <p className="text-gray-600 mt-2">
                Le commerce va examiner votre demande et vous contacter pour confirmer le rendez-vous.
              </p>
            </div>

            {businessInfo && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2">Détails du commerce</h3>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Commerce :</span> {businessInfo.businessName}</p>
                  {businessInfo.phone && (
                    <p><span className="font-medium">Téléphone :</span> {businessInfo.phone}</p>
                  )}
                  {businessInfo.address && (
                    <p><span className="font-medium">Adresse :</span> {businessInfo.address}</p>
                  )}
                </div>
              </div>
            )}

            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="font-medium text-blue-900 mb-2">Prochaines étapes</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Le commerce va examiner votre demande</li>
                <li>• Vous recevrez une confirmation par email ou téléphone</li>
                <li>• En cas de questions, contactez directement le commerce</li>
              </ul>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => window.history.back()}
              >
                Retour
              </Button>
              <Link href="/" className="flex-1">
                <Button className="w-full">
                  Retour à l'accueil
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
