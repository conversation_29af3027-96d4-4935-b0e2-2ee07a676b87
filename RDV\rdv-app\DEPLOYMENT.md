# 🚀 Guide de déploiement Planidoo sur Vercel

## Variables d'environnement requises

<PERSON><PERSON> de déploy<PERSON>, configurez ces variables d'environnement dans Vercel :

### 🔐 Authentification
```
NEXTAUTH_URL=https://votre-domaine.vercel.app
NEXTAUTH_SECRET=votre-secret-aleatoire-tres-long
```

### 🗄️ Base de données MongoDB
```
MONGODB_URI=mongodb+srv://username:<EMAIL>/planidoo
```

### 📧 Configuration email (optionnel)
```
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=votre-mot-de-passe-app
EMAIL_FROM=<EMAIL>
```

### 🔑 Google OAuth (optionnel)
```
GOOGLE_CLIENT_ID=votre-google-client-id
GOOGLE_CLIENT_SECRET=votre-google-client-secret
```

## 📋 Étapes de déploiement

1. **Connecter le repository à Vercel**
2. **Configurer les variables d'environnement**
3. **Déployer l'application**
4. **Configurer le nom de domaine personnalisé**

## 🌐 Configuration du domaine

Pour utiliser un domaine personnalisé comme `planido.com` :

1. Aller dans les paramètres du projet Vercel
2. Section "Domains"
3. Ajouter votre domaine
4. Configurer les DNS selon les instructions Vercel

## ✅ Vérifications post-déploiement

- [ ] Application accessible
- [ ] Authentification fonctionnelle
- [ ] Base de données connectée
- [ ] Emails envoyés (si configuré)
- [ ] Domaine personnalisé actif
