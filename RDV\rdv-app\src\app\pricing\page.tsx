import Link from 'next/link';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';

const plans = [
  {
    name: 'Freemium',
    price: 0,
    period: 'mois',
    description: 'Parfait pour commencer',
    features: [
      '10 réservations par mois',
      '3 services maximum',
      'Calendrier de base',
      'Support par email',
      '1 mois d\'essai gratuit'
    ],
    limitations: [
      'Pas de site vitrine',
      'Pas de notifications SMS',
      'Pas de rapports avancés'
    ],
    buttonText: 'Commencer gratuitement',
    buttonVariant: 'outline' as const,
    popular: false,
  },
  {
    name: 'Premium',
    price: 29,
    period: 'mois',
    description: 'Pour les commerces en croissance',
    features: [
      'Réservations illimitées',
      'Services illimités',
      'Site vitrine personnalisé',
      'Notifications SMS',
      'Rapports détaillés',
      'Support prioritaire',
      'Intégration Google Maps',
      'Réseaux sociaux'
    ],
    limitations: [],
    buttonText: 'Choisir Premium',
    buttonVariant: 'default' as const,
    popular: true,
  },
  {
    name: 'Enterprise',
    price: 99,
    period: 'mois',
    description: 'Pour les grandes entreprises',
    features: [
      'Tout du plan Premium',
      'Multi-établissements',
      'API personnalisée',
      'Support dédié 24/7',
      'Formation personnalisée',
      'Intégrations avancées',
      'Rapports personnalisés',
      'Sauvegarde prioritaire'
    ],
    limitations: [],
    buttonText: 'Contacter les ventes',
    buttonVariant: 'outline' as const,
    popular: false,
  },
];

export default async function PricingPage() {
  const session = await getServerSession(authOptions);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center relative">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-100 to-pink-100 rounded-3xl opacity-50 blur-3xl"></div>
          <div className="relative z-10 py-12">
            <h1 className="text-4xl font-bold sm:text-5xl">
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Tarifs simples et transparents
              </span>
            </h1>
            <p className="mt-6 text-xl text-gray-600">
              Choisissez le plan <span className="font-semibold text-purple-600">Planidoo</span> qui correspond à vos besoins. Commencez gratuitement !
            </p>
          </div>
        </div>

        {/* Plans */}
        <div className="mt-20 grid grid-cols-1 gap-8 lg:grid-cols-3">
          {plans.map((plan, index) => {
            const gradients = [
              'from-blue-50 to-indigo-100 border-blue-200',
              'from-purple-50 to-pink-100 border-purple-200',
              'from-orange-50 to-amber-100 border-orange-200'
            ];
            const badgeColors = [
              'bg-gradient-to-r from-blue-500 to-indigo-600',
              'bg-gradient-to-r from-purple-500 to-pink-600',
              'bg-gradient-to-r from-orange-500 to-amber-600'
            ];

            return (
              <Card
                key={plan.name}
                className={`relative bg-gradient-to-br ${gradients[index]} ${plan.popular ? 'ring-2 ring-purple-500 shadow-xl scale-105' : 'shadow-lg'} transition-all duration-200 hover:shadow-xl`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className={`${badgeColors[index]} text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg`}>
                      ⭐ Le plus populaire
                    </span>
                  </div>
                )}
              
                <CardHeader className="text-center">
                  <CardTitle className={`text-2xl font-bold ${index === 0 ? 'text-blue-900' : index === 1 ? 'text-purple-900' : 'text-orange-900'}`}>
                    {plan.name}
                  </CardTitle>
                  <div className="mt-4">
                    <span className={`text-5xl font-bold ${index === 0 ? 'text-blue-700' : index === 1 ? 'text-purple-700' : 'text-orange-700'}`}>
                      {plan.price}€
                    </span>
                    <span className="text-gray-600 text-lg">/{plan.period}</span>
                  </div>
                  <p className={`mt-3 font-medium ${index === 0 ? 'text-blue-700' : index === 1 ? 'text-purple-700' : 'text-orange-700'}`}>
                    {plan.description}
                  </p>
                </CardHeader>

              <CardContent className="space-y-6">
                {/* Features */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Inclus :</h4>
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <svg className="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Limitations */}
                {plan.limitations.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Limitations :</h4>
                    <ul className="space-y-2">
                      {plan.limitations.map((limitation, index) => (
                        <li key={index} className="flex items-center">
                          <svg className="h-5 w-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                          <span className="text-gray-700">{limitation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Button */}
                <div className="pt-6">
                  {session ? (
                    <Link href={`/dashboard/billing?plan=${plan.name.toLowerCase()}`}>
                      <Button
                        variant={plan.popular ? 'gradient' : index === 0 ? 'primary' : 'success'}
                        size="lg"
                        className="w-full"
                      >
                        {plan.buttonText}
                      </Button>
                    </Link>
                  ) : (
                    <Link href="/auth/signin">
                      <Button
                        variant={plan.popular ? 'gradient' : index === 0 ? 'primary' : 'success'}
                        size="lg"
                        className="w-full"
                      >
                        Commencer
                      </Button>
                    </Link>
                  )}
                </div>
              </CardContent>
            </Card>
            );
          })}
        </div>

        {/* FAQ Section */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Questions fréquentes
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">
                Puis-je changer de plan à tout moment ?
              </h3>
              <p className="text-gray-600">
                Oui, vous pouvez passer à un plan supérieur ou inférieur à tout moment. 
                Les changements prennent effet immédiatement.
              </p>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">
                Que se passe-t-il après la période d'essai ?
              </h3>
              <p className="text-gray-600">
                Après 1 mois d'essai gratuit, vous pouvez choisir de passer au plan Premium 
                ou continuer avec les limitations du plan Freemium.
              </p>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">
                Y a-t-il des frais cachés ?
              </h3>
              <p className="text-gray-600">
                Non, nos tarifs sont transparents. Aucun frais d'installation ou de résiliation.
              </p>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">
                Puis-je annuler mon abonnement ?
              </h3>
              <p className="text-gray-600">
                Oui, vous pouvez annuler votre abonnement à tout moment. 
                Vous gardez l'accès jusqu'à la fin de votre période payée.
              </p>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="mt-20 text-center relative">
          <div className="absolute inset-0 bg-gradient-to-r from-green-100 to-emerald-100 rounded-3xl opacity-50 blur-2xl"></div>
          <div className="relative z-10 py-12">
            <h2 className="text-3xl font-bold mb-4">
              <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                Prêt à commencer ?
              </span>
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Rejoignez des milliers de commerces qui font confiance à <span className="font-semibold text-purple-600">Planidoo</span>
            </p>
            <Link href={session ? "/dashboard" : "/auth/signin"}>
              <Button variant="success" size="lg" className="flex items-center space-x-2 mx-auto">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <span>{session ? "Aller au tableau de bord" : "Commencer gratuitement"}</span>
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export const metadata = {
  title: 'Tarifs - Planidoo',
  description: 'Découvrez nos plans tarifaires Planidoo. Commencez gratuitement avec notre offre freemium.',
};
