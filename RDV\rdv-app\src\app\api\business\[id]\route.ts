import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    await connectDB();
    
    const business = await User.findById(id).select(
      'name businessName phone address services schedule workingHours isVerified'
    );
    
    if (!business) {
      return NextResponse.json(
        { message: 'Commerce non trouvé' },
        { status: 404 }
      );
    }

    if (!business.isVerified) {
      return NextResponse.json(
        { message: 'Commerce non vérifié' },
        { status: 403 }
      );
    }

    // Convertir le schedule Map en objet simple
    let workingHours = {};

    if (business.schedule && business.schedule instanceof Map) {
      // Si c'est une Map MongoDB
      for (const [key, value] of business.schedule) {
        (workingHours as any)[key] = {
          start: value.start,
          end: value.end,
          isOpen: value.isOpen,
        };
      }
    } else if (business.schedule && typeof business.schedule === 'object') {
      // Si c'est déjà un objet
      workingHours = business.schedule;
    } else if (business.workingHours) {
      // Fallback vers workingHours
      workingHours = business.workingHours;
    }

    return NextResponse.json({
      _id: business._id.toString(),
      name: business.name,
      businessName: business.businessName,
      phone: business.phone || '',
      address: business.address || '',
      services: business.services || [],
      workingHours: workingHours,
    });
  } catch (error: any) {
    console.error('Erreur lors de la récupération du commerce:', error);
    return NextResponse.json(
      { message: 'Erreur serveur', error: error.message },
      { status: 500 }
    );
  }
}
