import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import DashboardNav from '@/components/DashboardNav';
import UserSettings from '@/components/UserSettings';

async function getUserData(userId: string) {
  await connectDB();
  const user = await User.findById(userId).select('name email businessName phone address services');
  
  if (!user) {
    return null;
  }

  return {
    _id: user._id.toString(),
    name: user.name,
    email: user.email,
    businessName: user.businessName,
    phone: user.phone || '',
    address: user.address || '',
    services: user.services || [],
  };
}

export default async function DashboardSettingsPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    redirect('/auth/signin');
  }

  const userData = await getUserData(session.user.id);

  if (!userData) {
    redirect('/auth/signin');
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardNav />
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Paramètres</h1>
            <p className="mt-2 text-gray-600">
              Gérez vos informations personnelles et commerciales
            </p>
          </div>

          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Informations du compte</CardTitle>
              </CardHeader>
              <CardContent>
                <UserSettings userData={userData} />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
