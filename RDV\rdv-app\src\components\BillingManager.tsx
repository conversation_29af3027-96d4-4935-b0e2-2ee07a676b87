'use client';

import { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import PaymentModal from '@/components/PaymentModal';

interface BillingData {
  user: {
    _id: string;
    subscriptionStatus: string;
    subscriptionPlan: string;
    trialStartDate?: string;
    trialEndDate?: string;
    subscriptionStartDate?: string;
    subscriptionEndDate?: string;
    maxBookingsPerMonth: number;
    maxServices: number;
  };
  payments: Array<{
    _id: string;
    amount: number;
    currency: string;
    status: string;
    subscriptionPlan: string;
    subscriptionPeriod: string;
    createdAt: string;
  }>;
}

interface BillingManagerProps {
  billingData: BillingData;
}

export default function BillingManager({ billingData }: BillingManagerProps) {
  const searchParams = useSearchParams();
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState('premium');
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');

  const { user, payments } = billingData;

  // Calculer les jours restants de l'essai
  const getDaysRemaining = () => {
    if (!user.trialEndDate) return 0;
    const endDate = new Date(user.trialEndDate);
    const today = new Date();
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'trial':
        return 'bg-blue-100 text-blue-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Actif';
      case 'trial':
        return 'Période d\'essai';
      case 'expired':
        return 'Expiré';
      case 'cancelled':
        return 'Annulé';
      default:
        return 'En attente';
    }
  };

  const handleUpgrade = (plan: string, period: string) => {
    setSelectedPlan(plan);
    setSelectedPeriod(period);
    setShowPaymentModal(true);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Non défini';
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Statut actuel */}
      <Card>
        <CardHeader>
          <CardTitle>Statut de votre abonnement</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(user.subscriptionStatus)}`}>
                  {getStatusText(user.subscriptionStatus)}
                </span>
                <span className="text-lg font-medium capitalize">{user.subscriptionPlan}</span>
              </div>
              
              {user.subscriptionStatus === 'trial' && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-blue-900 mb-2">Période d'essai gratuite</h4>
                  <p className="text-blue-800 text-sm">
                    Il vous reste <strong>{getDaysRemaining()} jours</strong> d'essai gratuit.
                  </p>
                  <p className="text-blue-700 text-sm mt-1">
                    Fin de l'essai : {formatDate(user.trialEndDate)}
                  </p>
                </div>
              )}

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Réservations par mois :</span>
                  <span className="font-medium">{user.maxBookingsPerMonth === -1 ? 'Illimitées' : user.maxBookingsPerMonth}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Services maximum :</span>
                  <span className="font-medium">{user.maxServices === -1 ? 'Illimités' : user.maxServices}</span>
                </div>
                {user.subscriptionEndDate && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Fin d'abonnement :</span>
                    <span className="font-medium">{formatDate(user.subscriptionEndDate)}</span>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-3">
              {user.subscriptionStatus === 'trial' && (
                <>
                  <Button 
                    onClick={() => handleUpgrade('premium', 'monthly')}
                    className="w-full"
                  >
                    Passer à Premium (29€/mois)
                  </Button>
                  <Button 
                    onClick={() => handleUpgrade('premium', 'yearly')}
                    variant="outline"
                    className="w-full"
                  >
                    Passer à Premium (290€/an) - 2 mois offerts
                  </Button>
                </>
              )}
              
              {user.subscriptionPlan === 'freemium' && user.subscriptionStatus !== 'trial' && (
                <Button 
                  onClick={() => handleUpgrade('premium', 'monthly')}
                  className="w-full"
                >
                  Passer à Premium
                </Button>
              )}

              {user.subscriptionPlan === 'premium' && (
                <Button 
                  onClick={() => handleUpgrade('enterprise', 'monthly')}
                  variant="outline"
                  className="w-full"
                >
                  Passer à Enterprise
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Historique des paiements */}
      <Card>
        <CardHeader>
          <CardTitle>Historique des paiements</CardTitle>
        </CardHeader>
        <CardContent>
          {payments.length === 0 ? (
            <p className="text-gray-500 text-center py-4">Aucun paiement pour le moment</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Plan
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Période
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Montant
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {payments.map((payment) => (
                    <tr key={payment._id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(payment.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                        {payment.subscriptionPlan}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {payment.subscriptionPeriod === 'monthly' ? 'Mensuel' : 'Annuel'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatAmount(payment.amount, payment.currency)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(payment.status)}`}>
                          {getStatusText(payment.status)}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modal de paiement */}
      {showPaymentModal && (
        <PaymentModal
          plan={selectedPlan}
          period={selectedPeriod}
          onClose={() => setShowPaymentModal(false)}
          onSuccess={() => {
            setShowPaymentModal(false);
            window.location.reload();
          }}
        />
      )}
    </div>
  );
}
