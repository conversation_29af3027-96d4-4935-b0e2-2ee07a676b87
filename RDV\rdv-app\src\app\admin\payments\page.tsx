import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import connectDB from '@/lib/mongodb';
import Payment from '@/models/Payment';
import AdminNav from '@/components/AdminNav';
import PaymentsManager from '@/components/admin/PaymentsManager';

async function getPayments() {
  await connectDB();
  const payments = await Payment.find()
    .populate('userId', 'name email businessName')
    .sort({ createdAt: -1 });
  return payments.map(payment => ({
    _id: payment._id.toString(),
    userId: {
      name: payment.userId.name,
      email: payment.userId.email,
      businessName: payment.userId.businessName,
    },
    amount: payment.amount,
    currency: payment.currency,
    status: payment.status,
    paymentMethod: payment.paymentMethod,
    subscriptionPlan: payment.subscriptionPlan,
    subscriptionPeriod: payment.subscriptionPeriod,
    startDate: payment.startDate.toISOString(),
    endDate: payment.endDate.toISOString(),
    createdAt: payment.createdAt.toISOString(),
  }));
}

async function getPaymentStats() {
  await connectDB();
  
  const [totalStats, statusStats, periodStats, planStats] = await Promise.all([
    // Statistiques totales
    Payment.aggregate([
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$amount' },
          totalCount: { $sum: 1 }
        }
      }
    ]),
    
    // Statistiques par statut
    Payment.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          amount: { $sum: '$amount' }
        }
      }
    ]),
    
    // Statistiques par période
    Payment.aggregate([
      {
        $group: {
          _id: '$subscriptionPeriod',
          count: { $sum: 1 },
          amount: { $sum: '$amount' }
        }
      }
    ]),
    
    // Statistiques par plan
    Payment.aggregate([
      {
        $group: {
          _id: '$subscriptionPlan',
          count: { $sum: 1 },
          amount: { $sum: '$amount' }
        }
      }
    ])
  ]);

  const stats = {
    totalAmount: totalStats[0]?.totalAmount || 0,
    totalCount: totalStats[0]?.totalCount || 0,
    byStatus: {
      pending: { count: 0, amount: 0 },
      completed: { count: 0, amount: 0 },
      failed: { count: 0, amount: 0 }
    },
    byPeriod: {
      monthly: { count: 0, amount: 0 },
      yearly: { count: 0, amount: 0 }
    },
    byPlan: {}
  };

  // Traiter les statistiques par statut
  statusStats.forEach(stat => {
    if (stat._id && (stats.byStatus as any)[stat._id]) {
      (stats.byStatus as any)[stat._id] = {
        count: stat.count,
        amount: stat.amount
      };
    }
  });

  // Traiter les statistiques par période
  periodStats.forEach(stat => {
    if (stat._id && (stats.byPeriod as any)[stat._id]) {
      (stats.byPeriod as any)[stat._id] = {
        count: stat.count,
        amount: stat.amount
      };
    }
  });

  // Traiter les statistiques par plan
  planStats.forEach(stat => {
    if (stat._id) {
      (stats.byPlan as any)[stat._id] = {
        count: stat.count,
        amount: stat.amount
      };
    }
  });

  console.log('Stats:', JSON.stringify(stats, null, 2));
  return stats;
}

export default async function PaymentsPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.isAdmin) {
    redirect('/');
  }

  const [payments, stats] = await Promise.all([
    getPayments(),
    getPaymentStats()
  ]);

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminNav />

      <main className="py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Gestion des paiements
          </h1>

          <div className="mt-8">
            <PaymentsManager
              initialPayments={payments}
              initialStats={stats}
            />
          </div>
        </div>
      </main>
    </div>
  );
} 