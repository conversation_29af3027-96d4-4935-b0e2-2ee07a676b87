# 🚀 Guide de Déploiement - Planidoo

## 🎯 Options de déploiement recommandées

### 1. 🔥 **V<PERSON><PERSON> (Recommandé - Gratuit)**

**Avantages :**
- ✅ Plateforme officielle Next.js
- ✅ Déploiement automatique depuis GitHub
- ✅ HTTPS gratuit
- ✅ CDN mondial
- ✅ Résolution automatique des problèmes de build

**Étapes :**

1. **Préparer le code :**
   ```bash
   git init
   git add .
   git commit -m "Initial commit - Planidoo app"
   ```

2. **Créer un repo GitHub :**
   - Aller sur github.com
   - Créer un nouveau repository "planidoo"
   - Pousser le code :
   ```bash
   git remote add origin https://github.com/VOTRE_USERNAME/planidoo.git
   git push -u origin main
   ```

3. **Déployer sur Vercel :**
   - Aller sur vercel.com
   - Se connecter avec GitHub
   - Importer le projet "planidoo"
   - Ajouter les variables d'environnement :
     ```
     MONGODB_URI=mongodb+srv://username:<EMAIL>/planidoo
     NEXTAUTH_SECRET=votre_secret_aleatoire
     NEXTAUTH_URL=https://votre-app.vercel.app
     ```
   - Cliquer "Deploy"

**URL finale :** `https://planidoo.vercel.app`

---

### 2. 🌐 **Netlify (Alternative gratuite)**

**Étapes :**
1. Connecter le repo GitHub à Netlify
2. Build command: `npm run build`
3. Publish directory: `.next`
4. Ajouter les variables d'environnement

---

### 3. ☁️ **Railway (Base de données incluse)**

**Avantages :**
- ✅ MongoDB inclus
- ✅ Déploiement simple
- ✅ Plan gratuit généreux

**Étapes :**
1. Aller sur railway.app
2. Connecter GitHub
3. Déployer le projet
4. Ajouter un service MongoDB
5. Configurer les variables d'environnement

---

### 4. 🐳 **Docker (Pour serveur personnel)**

**Dockerfile :**
```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

**docker-compose.yml :**
```yaml
version: '3.8'
services:
  planidoo:
    build: .
    ports:
      - "3000:3000"
    environment:
      - MONGODB_URI=mongodb://mongo:27017/planidoo
      - NEXTAUTH_SECRET=your_secret
      - NEXTAUTH_URL=http://localhost:3000
    depends_on:
      - mongo
  
  mongo:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

volumes:
  mongo_data:
```

---

## 🔧 **Configuration de la base de données**

### **MongoDB Atlas (Gratuit - Recommandé)**

1. Aller sur mongodb.com/atlas
2. Créer un compte gratuit
3. Créer un cluster (M0 - Gratuit)
4. Créer un utilisateur de base de données
5. Autoriser l'accès depuis n'importe où (0.0.0.0/0)
6. Récupérer l'URI de connexion

**Format URI :**
```
mongodb+srv://username:<EMAIL>/planidoo?retryWrites=true&w=majority
```

---

## 🌍 **Variables d'environnement de production**

```env
# Base de données
MONGODB_URI=mongodb+srv://username:<EMAIL>/planidoo

# Authentification
NEXTAUTH_SECRET=un_secret_tres_long_et_aleatoire_de_32_caracteres_minimum
NEXTAUTH_URL=https://votre-domaine.com

# Email (optionnel)
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=votre-mot-de-passe-app
EMAIL_FROM=<EMAIL>

# Paiements (optionnel)
STRIPE_PUBLIC_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

---

## 🎨 **Optimisations pour la production**

### **Performance :**
- ✅ Images optimisées avec Next.js Image
- ✅ CSS minifié automatiquement
- ✅ JavaScript bundlé et optimisé
- ✅ Lazy loading des composants

### **SEO :**
- ✅ Métadonnées configurées
- ✅ Sitemap automatique
- ✅ Robots.txt
- ✅ Open Graph tags

### **Accessibilité :**
- ✅ Mode sombre/clair
- ✅ Support RTL (arabe)
- ✅ Text-to-Speech
- ✅ Navigation clavier
- ✅ ARIA labels

---

## 📊 **Monitoring et Analytics**

### **Vercel Analytics (Gratuit) :**
```bash
npm install @vercel/analytics
```

### **Google Analytics :**
Ajouter dans `layout.tsx` :
```jsx
import { GoogleAnalytics } from '@next/third-parties/google'

export default function RootLayout({ children }) {
  return (
    <html>
      <body>{children}</body>
      <GoogleAnalytics gaId="GA_MEASUREMENT_ID" />
    </html>
  )
}
```

---

## 🔒 **Sécurité**

### **Headers de sécurité (déjà configurés) :**
- ✅ X-Content-Type-Options
- ✅ X-Frame-Options
- ✅ X-XSS-Protection

### **HTTPS :**
- ✅ Automatique sur Vercel/Netlify
- ✅ Certificats SSL gratuits

---

## 🚀 **Déploiement en un clic**

**Pour Vercel :**
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/VOTRE_USERNAME/planidoo)

**Pour Netlify :**
[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/VOTRE_USERNAME/planidoo)

---

## 📞 **Support post-déploiement**

### **Logs et debugging :**
- Vercel : Dashboard > Functions > Logs
- Netlify : Dashboard > Functions > Logs
- Railway : Dashboard > Deployments > Logs

### **Monitoring :**
- Uptime monitoring avec UptimeRobot
- Error tracking avec Sentry
- Performance avec Vercel Analytics

---

## ✅ **Checklist de déploiement**

- [ ] Code poussé sur GitHub
- [ ] Variables d'environnement configurées
- [ ] Base de données MongoDB Atlas créée
- [ ] Domaine personnalisé configuré (optionnel)
- [ ] SSL/HTTPS activé
- [ ] Tests de fonctionnalité effectués
- [ ] Analytics configurés
- [ ] Monitoring activé

---

**🎉 Votre application Planidoo sera accessible à l'adresse :**
`https://planidoo.vercel.app` ou votre domaine personnalisé !

**Temps estimé de déploiement :** 15-30 minutes
