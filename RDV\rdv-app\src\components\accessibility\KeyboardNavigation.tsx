'use client';

import { useEffect, useState } from 'react';

export default function KeyboardNavigation() {
  const [isKeyboardUser, setIsKeyboardUser] = useState(false);
  const [showHelp, setShowHelp] = useState(false);

  useEffect(() => {
    let keyboardTimeout: NodeJS.Timeout;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Détecter l'utilisation du clavier
      if (e.key === 'Tab' || e.key === 'Enter' || e.key === ' ' || e.key.startsWith('Arrow')) {
        setIsKeyboardUser(true);
        
        // Réinitialiser après 5 secondes d'inactivité
        clearTimeout(keyboardTimeout);
        keyboardTimeout = setTimeout(() => {
          setIsKeyboardUser(false);
        }, 5000);
      }

      // Raccourcis clavier globaux
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'h':
            e.preventDefault();
            setShowHelp(!showHelp);
            break;
          case '/':
            e.preventDefault();
            // Focus sur le champ de recherche s'il existe
            const searchInput = document.querySelector('input[type="search"], input[placeholder*="recherch"]') as HTMLInputElement;
            if (searchInput) {
              searchInput.focus();
            }
            break;
          case 'k':
            e.preventDefault();
            // Ouvrir le menu de navigation rapide
            const navButton = document.querySelector('[aria-label*="menu"], [aria-label*="navigation"]') as HTMLButtonElement;
            if (navButton) {
              navButton.click();
            }
            break;
        }
      }

      // Échapper pour fermer les modales/menus
      if (e.key === 'Escape') {
        setShowHelp(false);
        // Fermer les menus ouverts
        const openMenus = document.querySelectorAll('[aria-expanded="true"]');
        openMenus.forEach(menu => {
          if (menu instanceof HTMLButtonElement) {
            menu.click();
          }
        });
      }
    };

    const handleMouseDown = () => {
      setIsKeyboardUser(false);
      clearTimeout(keyboardTimeout);
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
      clearTimeout(keyboardTimeout);
    };
  }, [showHelp]);

  // Ajouter des styles CSS pour améliorer la visibilité du focus
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .keyboard-user *:focus {
        outline: 3px solid #8B5CF6 !important;
        outline-offset: 2px !important;
        border-radius: 4px;
      }
      
      .keyboard-user button:focus,
      .keyboard-user a:focus,
      .keyboard-user input:focus,
      .keyboard-user textarea:focus,
      .keyboard-user select:focus {
        box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3) !important;
      }

      .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: #8B5CF6;
        color: white;
        padding: 8px 16px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1000;
        font-weight: 600;
        transition: top 0.3s;
      }

      .skip-link:focus {
        top: 6px;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Appliquer la classe CSS conditionnellement
  useEffect(() => {
    if (isKeyboardUser) {
      document.body.classList.add('keyboard-user');
    } else {
      document.body.classList.remove('keyboard-user');
    }
  }, [isKeyboardUser]);

  return (
    <>
      {/* Lien de saut au contenu principal */}
      <a 
        href="#main-content" 
        className="skip-link"
        onFocus={() => setIsKeyboardUser(true)}
      >
        Aller au contenu principal
      </a>

      {/* Indicateur de navigation clavier */}
      {isKeyboardUser && (
        <div 
          className="fixed top-4 right-4 bg-purple-600 text-white px-3 py-2 rounded-lg shadow-lg z-50 text-sm font-medium"
          role="status"
          aria-live="polite"
        >
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            <span>Navigation clavier active</span>
            <button
              onClick={() => setShowHelp(!showHelp)}
              className="ml-2 text-purple-200 hover:text-white"
              aria-label="Afficher l'aide des raccourcis clavier"
            >
              ?
            </button>
          </div>
        </div>
      )}

      {/* Aide des raccourcis clavier */}
      {showHelp && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          role="dialog"
          aria-modal="true"
          aria-labelledby="keyboard-help-title"
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 id="keyboard-help-title" className="text-lg font-semibold text-gray-900 dark:text-white">
                Raccourcis clavier
              </h2>
              <button
                onClick={() => setShowHelp(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                aria-label="Fermer l'aide"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Navigation</span>
                <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">Tab</kbd>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Recherche</span>
                <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">Ctrl + /</kbd>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Menu navigation</span>
                <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">Ctrl + K</kbd>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Aide</span>
                <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">Ctrl + H</kbd>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Fermer</span>
                <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">Échap</kbd>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
