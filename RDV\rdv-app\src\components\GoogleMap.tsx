'use client';

import { useEffect, useRef } from 'react';

interface GoogleMapProps {
  latitude: number;
  longitude: number;
  businessName: string;
  address: string;
  height?: string;
}

declare global {
  interface Window {
    google: any;
    initMap: () => void;
  }
}

export default function GoogleMap({ 
  latitude, 
  longitude, 
  businessName, 
  address, 
  height = '300px' 
}: GoogleMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);

  useEffect(() => {
    const loadGoogleMaps = () => {
      if (window.google && window.google.maps) {
        initializeMap();
        return;
      }

      // Créer la fonction globale initMap
      window.initMap = initializeMap;

      // Charger le script Google Maps
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&callback=initMap`;
      script.async = true;
      script.defer = true;
      document.head.appendChild(script);

      return () => {
        document.head.removeChild(script);
        delete window.initMap;
      };
    };

    const initializeMap = () => {
      if (!mapRef.current || !window.google) return;

      const mapOptions = {
        center: { lat: latitude, lng: longitude },
        zoom: 15,
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: true,
        zoomControl: true,
        styles: [
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'off' }]
          }
        ]
      };

      mapInstanceRef.current = new window.google.maps.Map(mapRef.current, mapOptions);

      // Ajouter un marqueur
      const marker = new window.google.maps.Marker({
        position: { lat: latitude, lng: longitude },
        map: mapInstanceRef.current,
        title: businessName,
        animation: window.google.maps.Animation.DROP,
      });

      // Ajouter une info window
      const infoWindow = new window.google.maps.InfoWindow({
        content: `
          <div style="padding: 10px; max-width: 200px;">
            <h3 style="margin: 0 0 5px 0; font-size: 16px; font-weight: bold;">${businessName}</h3>
            <p style="margin: 0; font-size: 14px; color: #666;">${address}</p>
            <a 
              href="https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}" 
              target="_blank" 
              style="color: #1a73e8; text-decoration: none; font-size: 14px;"
            >
              Obtenir l'itinéraire
            </a>
          </div>
        `
      });

      marker.addListener('click', () => {
        infoWindow.open(mapInstanceRef.current, marker);
      });

      // Ouvrir l'info window par défaut
      infoWindow.open(mapInstanceRef.current, marker);
    };

    loadGoogleMaps();
  }, [latitude, longitude, businessName, address]);

  // Fallback si Google Maps n'est pas disponible
  const openInGoogleMaps = () => {
    const url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
    window.open(url, '_blank');
  };

  return (
    <div className="relative">
      <div
        ref={mapRef}
        style={{ height }}
        className="w-full rounded-lg"
      />
      {/* Fallback button */}
      <div className="absolute bottom-2 right-2">
        <button
          onClick={openInGoogleMaps}
          className="bg-white shadow-md rounded-lg px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Ouvrir dans Maps
        </button>
      </div>
      
      {/* Loading placeholder */}
      {(typeof window === 'undefined' || !window.google) && (
        <div
          className="absolute inset-0 bg-gray-200 rounded-lg flex items-center justify-center"
          style={{ height }}
        >
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-gray-600 text-sm">Chargement de la carte...</p>
          </div>
        </div>
      )}
    </div>
  );
}
