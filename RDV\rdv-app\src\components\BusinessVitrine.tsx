'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import GoogleMap from '@/components/GoogleMap';

interface Business {
  _id: string;
  businessName: string;
  businessDescription: string;
  businessImages: string[];
  socialMedia: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
    website?: string;
  };
  coordinates: {
    latitude: number;
    longitude: number;
  };
  businessCategory: string;
  businessTags: string[];
  phone: string;
  address: string;
  services: string[];
  schedule: {
    [key: string]: {
      start: string;
      end: string;
      isOpen: boolean;
    };
  };
}

interface BusinessVitrineProps {
  business: Business;
}

const DAYS_FR = {
  monday: 'Lundi',
  tuesday: 'Mardi',
  wednesday: 'Mercredi',
  thursday: 'Jeudi',
  friday: 'Vendredi',
  saturday: 'Samedi',
  sunday: 'Dimanche',
};

export default function BusinessVitrine({ business }: BusinessVitrineProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const getSocialIcon = (platform: string) => {
    const icons = {
      facebook: '📘',
      instagram: '📷',
      twitter: '🐦',
      linkedin: '💼',
      website: '🌐',
    };
    return icons[platform as keyof typeof icons] || '🔗';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">{business.businessName}</h1>
            <Link
              href={`/book/${business._id}`}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Prendre rendez-vous
            </Link>
          </div>
          {business.businessCategory && (
            <p className="text-gray-600 mt-1">{business.businessCategory}</p>
          )}
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Colonne principale */}
          <div className="lg:col-span-2 space-y-8">
            {/* Images */}
            {business.businessImages.length > 0 && (
              <Card>
                <CardContent className="p-0">
                  <div className="relative h-96">
                    <Image
                      src={business.businessImages[selectedImageIndex]}
                      alt={business.businessName}
                      fill
                      className="object-cover rounded-t-lg"
                    />
                  </div>
                  {business.businessImages.length > 1 && (
                    <div className="flex space-x-2 p-4 overflow-x-auto">
                      {business.businessImages.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setSelectedImageIndex(index)}
                          className={`flex-shrink-0 w-20 h-20 relative rounded-lg overflow-hidden border-2 ${
                            selectedImageIndex === index ? 'border-blue-500' : 'border-gray-200'
                          }`}
                        >
                          <Image
                            src={image}
                            alt={`${business.businessName} ${index + 1}`}
                            fill
                            className="object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Description */}
            {business.businessDescription && (
              <Card>
                <CardHeader>
                  <CardTitle>À propos</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 whitespace-pre-line">{business.businessDescription}</p>
                </CardContent>
              </Card>
            )}

            {/* Services */}
            {business.services.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Nos services</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {business.services.map((service, index) => (
                      <div key={index} className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="font-medium text-gray-900">{service}</h3>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Tags */}
            {business.businessTags.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Spécialités</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {business.businessTags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Informations de contact */}
            <Card>
              <CardHeader>
                <CardTitle>Contact</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {business.phone && (
                  <div className="flex items-center space-x-3">
                    <span className="text-gray-400">📞</span>
                    <a href={`tel:${business.phone}`} className="text-blue-600 hover:underline">
                      {business.phone}
                    </a>
                  </div>
                )}
                {business.address && (
                  <div className="flex items-start space-x-3">
                    <span className="text-gray-400 mt-1">📍</span>
                    <p className="text-gray-700">{business.address}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Horaires */}
            <Card>
              <CardHeader>
                <CardTitle>Horaires d'ouverture</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(DAYS_FR).map(([dayKey, dayLabel]) => {
                    const daySchedule = business.schedule[dayKey];
                    return (
                      <div key={dayKey} className="flex justify-between">
                        <span className="text-gray-700">{dayLabel}</span>
                        <span className="text-gray-900">
                          {daySchedule?.isOpen
                            ? `${daySchedule.start} - ${daySchedule.end}`
                            : 'Fermé'}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Réseaux sociaux */}
            {Object.values(business.socialMedia).some(url => url) && (
              <Card>
                <CardHeader>
                  <CardTitle>Suivez-nous</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(business.socialMedia).map(([platform, url]) => {
                      if (!url) return null;
                      return (
                        <a
                          key={platform}
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-3 text-gray-700 hover:text-blue-600 transition-colors"
                        >
                          <span>{getSocialIcon(platform)}</span>
                          <span className="capitalize">{platform}</span>
                        </a>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Carte */}
            {business.coordinates.latitude !== 0 && business.coordinates.longitude !== 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Localisation</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <GoogleMap
                    latitude={business.coordinates.latitude}
                    longitude={business.coordinates.longitude}
                    businessName={business.businessName}
                    address={business.address}
                  />
                </CardContent>
              </Card>
            )}

            {/* Bouton de réservation */}
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="text-center py-6">
                <h3 className="font-medium text-gray-900 mb-2">Prêt à réserver ?</h3>
                <p className="text-gray-600 text-sm mb-4">
                  Prenez rendez-vous en quelques clics
                </p>
                <Link
                  href={`/book/${business._id}`}
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-block"
                >
                  Réserver maintenant
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
