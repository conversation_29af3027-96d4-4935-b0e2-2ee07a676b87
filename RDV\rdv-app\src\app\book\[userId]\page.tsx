'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

interface BusinessInfo {
  _id: string;
  name: string;
  businessName: string;
  phone: string;
  address: string;
  services: string[];
  workingHours: {
    [key: string]: {
      start: string;
      end: string;
      isOpen: boolean;
    };
  };
}

export default function BookingPage() {
  const params = useParams();
  const [businessInfo, setBusinessInfo] = useState<BusinessInfo | null>(null);
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [availableSlots, setAvailableSlots] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    clientName: '',
    clientEmail: '',
    service: '',
    notes: '',
  });

  useEffect(() => {
    const fetchBusinessInfo = async () => {
      try {
        const response = await fetch(`/api/business/${params.userId}`);
        if (!response.ok) {
          throw new Error('Commerce non trouvé');
        }
        const data = await response.json();
        setBusinessInfo(data);
      } catch (error) {
        console.error('Erreur lors du chargement:', error);
        setError('Erreur lors du chargement des informations du commerce');
      }
    };

    fetchBusinessInfo();
  }, [params.userId]);

  useEffect(() => {
    if (selectedDate && businessInfo) {
      // Convertir le jour français en anglais pour correspondre aux clés de la base de données
      const date = new Date(selectedDate);
      const dayOfWeek = date.getDay(); // 0 = dimanche, 1 = lundi, etc.

      const dayMapping = {
        0: 'sunday',
        1: 'monday',
        2: 'tuesday',
        3: 'wednesday',
        4: 'thursday',
        5: 'friday',
        6: 'saturday'
      };

      const dayKey = dayMapping[dayOfWeek as keyof typeof dayMapping];
      const daySchedule = businessInfo.workingHours[dayKey];

      if (daySchedule?.isOpen) {
        const slots = generateTimeSlots(daySchedule.start, daySchedule.end);
        setAvailableSlots(slots);
      } else {
        setAvailableSlots([]);
      }
    }
  }, [selectedDate, businessInfo]);

  const generateTimeSlots = (start: string, end: string) => {
    const slots: string[] = [];
    const [startHour, startMinute] = start.split(':').map(Number);
    const [endHour, endMinute] = end.split(':').map(Number);

    let currentHour = startHour;
    let currentMinute = startMinute;

    while (
      currentHour < endHour ||
      (currentHour === endHour && currentMinute < endMinute)
    ) {
      slots.push(
        `${currentHour.toString().padStart(2, '0')}:${currentMinute
          .toString()
          .padStart(2, '0')}`
      );

      currentMinute += 30;
      if (currentMinute >= 60) {
        currentHour += 1;
        currentMinute = 0;
      }
    }

    return slots;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Validation côté client
    if (!formData.clientName.trim()) {
      setError('Le nom est requis');
      setIsLoading(false);
      return;
    }

    if (!formData.clientEmail.trim()) {
      setError('L\'email est requis');
      setIsLoading(false);
      return;
    }

    if (!selectedDate) {
      setError('La date est requise');
      setIsLoading(false);
      return;
    }

    if (!selectedTime) {
      setError('L\'heure est requise');
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: params.userId,
          clientName: formData.clientName.trim(),
          clientEmail: formData.clientEmail.trim(),
          service: formData.service || 'Rendez-vous',
          notes: formData.notes.trim(),
          date: selectedDate,
          time: selectedTime,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Une erreur est survenue lors de la réservation');
      }

      // Rediriger vers la page de confirmation
      window.location.href = `/book/${params.userId}/confirmation`;
    } catch (err: any) {
      console.error('Erreur lors de la réservation:', err);
      setError(err.message || 'Une erreur inattendue est survenue');
    } finally {
      setIsLoading(false);
    }
  };

  if (error && !businessInfo) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-xl font-semibold text-red-600 mb-4">Erreur</div>
          <div className="text-gray-600">{error}</div>
          <button
            onClick={() => window.history.back()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retour
          </button>
        </div>
      </div>
    );
  }

  if (!businessInfo) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-xl font-semibold text-gray-900">Chargement...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="text-center text-2xl font-bold">
              Réserver un rendez-vous chez {businessInfo.businessName}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <Input
                  label="Date"
                  type="date"
                  required
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Heure
                  </label>
                  <select
                    value={selectedTime}
                    onChange={(e) => setSelectedTime(e.target.value)}
                    required
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  >
                    <option value="">
                      {selectedDate
                        ? (availableSlots.length > 0 ? 'Sélectionner une heure' : 'Aucun créneau disponible')
                        : 'Sélectionnez d\'abord une date'
                      }
                    </option>
                    {availableSlots.map((slot) => (
                      <option key={slot} value={slot}>
                        {slot}
                      </option>
                    ))}
                  </select>
                  {selectedDate && availableSlots.length === 0 && (
                    <p className="text-sm text-gray-500 mt-1">
                      Aucun créneau disponible pour cette date. Le commerce est peut-être fermé.
                    </p>
                  )}
                </div>
              </div>

              <Input
                label="Nom"
                type="text"
                required
                value={formData.clientName}
                onChange={(e) =>
                  setFormData({ ...formData, clientName: e.target.value })
                }
              />

              <Input
                label="Email"
                type="email"
                required
                value={formData.clientEmail}
                onChange={(e) =>
                  setFormData({ ...formData, clientEmail: e.target.value })
                }
              />

              {businessInfo?.services && businessInfo.services.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Service souhaité
                  </label>
                  <select
                    value={formData.service}
                    onChange={(e) => setFormData({ ...formData, service: e.target.value })}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  >
                    <option value="">Sélectionner un service</option>
                    {businessInfo.services.map((service) => (
                      <option key={service} value={service}>
                        {service}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes (optionnel)
                </label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  rows={3}
                  placeholder="Précisions sur votre demande..."
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                />
              </div>

              {error && (
                <div className="text-red-600 text-sm">{error}</div>
              )}

              <Button
                type="submit"
                className="w-full"
                isLoading={isLoading}
                disabled={!selectedDate || !selectedTime}
              >
                Réserver
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 