{"name": "planidoo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3002", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "seed": "node scripts/seed-data.js", "seed:dev": "MONGODB_URI=mongodb://localhost:27017/planidoo node scripts/seed-data.js"}, "dependencies": {"@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "mongoose": "^8.15.1", "next": "15.3.3", "next-auth": "^4.24.11", "next-intl": "^3.22.0", "next-themes": "^0.4.4", "nodemailer": "^6.10.1", "postcss": "^8.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "typescript": "^5"}}