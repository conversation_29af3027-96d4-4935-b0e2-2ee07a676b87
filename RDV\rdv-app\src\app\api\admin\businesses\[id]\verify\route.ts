import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';

export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: 'Non autorisé' },
        { status: 401 }
      );
    }

    const { id } = await params;
    await connectDB();

    const user = await User.findByIdAndUpdate(
      id,
      { isVerified: true },
      { new: true }
    );

    if (!user) {
      return NextResponse.json(
        { message: 'Commerce non trouvé' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { message: 'Commerce vérifié avec succès' },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('Erreur lors de la vérification du commerce:', error);
    return NextResponse.json(
      { message: 'Une erreur est survenue lors de la vérification du commerce' },
      { status: 500 }
    );
  }
} 