{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/RDV/rdv-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\r\n  variant?: 'primary' | 'secondary' | 'outline';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  className,\r\n  variant = 'primary',\r\n  size = 'md',\r\n  isLoading = false,\r\n  disabled,\r\n  ...props\r\n}) => {\r\n  const baseStyles = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\r\n  \r\n  const variants = {\r\n    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\r\n    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',\r\n    outline: 'border border-gray-300 bg-transparent hover:bg-gray-50 focus:ring-gray-500',\r\n  };\r\n\r\n  const sizes = {\r\n    sm: 'h-8 px-3 text-sm',\r\n    md: 'h-10 px-4 text-base',\r\n    lg: 'h-12 px-6 text-lg',\r\n  };\r\n\r\n  return (\r\n    <button\r\n      className={twMerge(\r\n        baseStyles,\r\n        variants[variant],\r\n        sizes[size],\r\n        className\r\n      )}\r\n      disabled={disabled || isLoading}\r\n      {...props}\r\n    >\r\n      {isLoading ? (\r\n        <div className=\"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent\" />\r\n      ) : null}\r\n      {children}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default Button; "], "names": [], "mappings": ";;;;AACA;;;AAQA,MAAM,SAAgC,CAAC,EACrC,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EACf,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,0BACC,6LAAC;gBAAI,WAAU;;;;;uBACb;YACH;;;;;;;AAGP;KAxCM;uCA0CS", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/RDV/rdv-app/src/components/AdminNav.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { usePathname } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport { signOut } from 'next-auth/react';\r\nimport Button from '@/components/ui/Button';\r\n\r\nconst navigation = [\r\n  { name: 'Tableau de bord', href: '/admin' },\r\n  { name: 'Commerces', href: '/admin/businesses' },\r\n  { name: 'Paiements', href: '/admin/payments' },\r\n  { name: 'Paramètres', href: '/admin/settings' },\r\n];\r\n\r\nexport default function AdminNav() {\r\n  const pathname = usePathname();\r\n\r\n  return (\r\n    <nav className=\"bg-white shadow\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"flex justify-between h-16\">\r\n          <div className=\"flex\">\r\n            <div className=\"flex-shrink-0 flex items-center\">\r\n              <Link href=\"/admin\" className=\"text-xl font-bold text-blue-600\">\r\n                Admin Panel\r\n              </Link>\r\n            </div>\r\n            <div className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\r\n              {navigation.map((item) => {\r\n                const isActive = pathname === item.href;\r\n                return (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${\r\n                      isActive\r\n                        ? 'border-blue-500 text-gray-900'\r\n                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'\r\n                    }`}\r\n                  >\r\n                    {item.name}\r\n                  </Link>\r\n                );\r\n              })}\r\n            </div>\r\n          </div>\r\n          <div className=\"hidden sm:ml-6 sm:flex sm:items-center\">\r\n            <Button\r\n              variant=\"outline\"\r\n              onClick={() => signOut({ callbackUrl: '/' })}\r\n            >\r\n              Se déconnecter\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAmB,MAAM;IAAS;IAC1C;QAAE,MAAM;QAAa,MAAM;IAAoB;IAC/C;QAAE,MAAM;QAAa,MAAM;IAAkB;IAC7C;QAAE,MAAM;QAAc,MAAM;IAAkB;CAC/C;AAEc,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAkC;;;;;;;;;;;0CAIlE,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,kEAAkE,EAC5E,WACI,kCACA,8EACJ;kDAED,KAAK,IAAI;uCARL,KAAK,IAAI;;;;;gCAWpB;;;;;;;;;;;;kCAGJ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,UAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;oCAAE,aAAa;gCAAI;sCAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA5CwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/RDV/rdv-app/src/components/admin/PaymentFilters.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\n\r\ninterface PaymentFiltersProps {\r\n  onFilterChange: (filters: {\r\n    status?: string;\r\n    startDate?: string;\r\n    endDate?: string;\r\n    minAmount?: number;\r\n    maxAmount?: number;\r\n  }) => void;\r\n}\r\n\r\nexport default function PaymentFilters({ onFilterChange }: PaymentFiltersProps) {\r\n  const [filters, setFilters] = useState({\r\n    status: '',\r\n    startDate: '',\r\n    endDate: '',\r\n    minAmount: '',\r\n    maxAmount: '',\r\n  });\r\n\r\n  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {\r\n    const { name, value } = e.target;\r\n    const newFilters = { ...filters, [name]: value };\r\n    setFilters(newFilters);\r\n\r\n    // Convertir les valeurs numériques et nettoyer les valeurs vides\r\n    const processedFilters: any = {};\r\n\r\n    if (newFilters.status && newFilters.status !== '') {\r\n      processedFilters.status = newFilters.status;\r\n    }\r\n\r\n    if (newFilters.startDate && newFilters.startDate !== '') {\r\n      processedFilters.startDate = newFilters.startDate;\r\n    }\r\n\r\n    if (newFilters.endDate && newFilters.endDate !== '') {\r\n      processedFilters.endDate = newFilters.endDate;\r\n    }\r\n\r\n    if (newFilters.minAmount && newFilters.minAmount !== '') {\r\n      processedFilters.minAmount = Number(newFilters.minAmount);\r\n    }\r\n\r\n    if (newFilters.maxAmount && newFilters.maxAmount !== '') {\r\n      processedFilters.maxAmount = Number(newFilters.maxAmount);\r\n    }\r\n\r\n    onFilterChange(processedFilters);\r\n  };\r\n\r\n  const handleReset = () => {\r\n    const resetFilters = {\r\n      status: '',\r\n      startDate: '',\r\n      endDate: '',\r\n      minAmount: '',\r\n      maxAmount: '',\r\n    };\r\n    setFilters(resetFilters);\r\n    onFilterChange({});\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white p-4 rounded-lg shadow mb-6\">\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <h3 className=\"text-lg font-medium text-gray-900\">Filtres</h3>\r\n        <button\r\n          onClick={handleReset}\r\n          className=\"px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:bg-gray-50\"\r\n        >\r\n          Réinitialiser\r\n        </button>\r\n      </div>\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\r\n        <div>\r\n          <label htmlFor=\"status\" className=\"block text-sm font-medium text-gray-700\">\r\n            Statut\r\n          </label>\r\n          <select\r\n            id=\"status\"\r\n            name=\"status\"\r\n            value={filters.status}\r\n            onChange={handleFilterChange}\r\n            className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\"\r\n          >\r\n            <option value=\"\">Tous</option>\r\n            <option value=\"pending\">En attente</option>\r\n            <option value=\"completed\">Complété</option>\r\n            <option value=\"failed\">Échoué</option>\r\n          </select>\r\n        </div>\r\n\r\n        <div>\r\n          <label htmlFor=\"startDate\" className=\"block text-sm font-medium text-gray-700\">\r\n            Date de début\r\n          </label>\r\n          <input\r\n            type=\"date\"\r\n            id=\"startDate\"\r\n            name=\"startDate\"\r\n            value={filters.startDate}\r\n            onChange={handleFilterChange}\r\n            className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\"\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <label htmlFor=\"endDate\" className=\"block text-sm font-medium text-gray-700\">\r\n            Date de fin\r\n          </label>\r\n          <input\r\n            type=\"date\"\r\n            id=\"endDate\"\r\n            name=\"endDate\"\r\n            value={filters.endDate}\r\n            onChange={handleFilterChange}\r\n            className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\"\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <label htmlFor=\"minAmount\" className=\"block text-sm font-medium text-gray-700\">\r\n            Montant minimum\r\n          </label>\r\n          <input\r\n            type=\"number\"\r\n            id=\"minAmount\"\r\n            name=\"minAmount\"\r\n            value={filters.minAmount}\r\n            onChange={handleFilterChange}\r\n            className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\"\r\n            min=\"0\"\r\n            step=\"0.01\"\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <label htmlFor=\"maxAmount\" className=\"block text-sm font-medium text-gray-700\">\r\n            Montant maximum\r\n          </label>\r\n          <input\r\n            type=\"number\"\r\n            id=\"maxAmount\"\r\n            name=\"maxAmount\"\r\n            value={filters.maxAmount}\r\n            onChange={handleFilterChange}\r\n            className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\"\r\n            min=\"0\"\r\n            step=\"0.01\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAce,SAAS,eAAe,EAAE,cAAc,EAAuB;;IAC5E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,QAAQ;QACR,WAAW;QACX,SAAS;QACT,WAAW;QACX,WAAW;IACb;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,MAAM,aAAa;YAAE,GAAG,OAAO;YAAE,CAAC,KAAK,EAAE;QAAM;QAC/C,WAAW;QAEX,iEAAiE;QACjE,MAAM,mBAAwB,CAAC;QAE/B,IAAI,WAAW,MAAM,IAAI,WAAW,MAAM,KAAK,IAAI;YACjD,iBAAiB,MAAM,GAAG,WAAW,MAAM;QAC7C;QAEA,IAAI,WAAW,SAAS,IAAI,WAAW,SAAS,KAAK,IAAI;YACvD,iBAAiB,SAAS,GAAG,WAAW,SAAS;QACnD;QAEA,IAAI,WAAW,OAAO,IAAI,WAAW,OAAO,KAAK,IAAI;YACnD,iBAAiB,OAAO,GAAG,WAAW,OAAO;QAC/C;QAEA,IAAI,WAAW,SAAS,IAAI,WAAW,SAAS,KAAK,IAAI;YACvD,iBAAiB,SAAS,GAAG,OAAO,WAAW,SAAS;QAC1D;QAEA,IAAI,WAAW,SAAS,IAAI,WAAW,SAAS,KAAK,IAAI;YACvD,iBAAiB,SAAS,GAAG,OAAO,WAAW,SAAS;QAC1D;QAEA,eAAe;IACjB;IAEA,MAAM,cAAc;QAClB,MAAM,eAAe;YACnB,QAAQ;YACR,WAAW;YACX,SAAS;YACT,WAAW;YACX,WAAW;QACb;QACA,WAAW;QACX,eAAe,CAAC;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAIH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAS,WAAU;0CAA0C;;;;;;0CAG5E,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,QAAQ,MAAM;gCACrB,UAAU;gCACV,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;;;;;;;kCAI3B,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAY,WAAU;0CAA0C;;;;;;0CAG/E,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,QAAQ,SAAS;gCACxB,UAAU;gCACV,WAAU;;;;;;;;;;;;kCAId,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAU,WAAU;0CAA0C;;;;;;0CAG7E,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,QAAQ,OAAO;gCACtB,UAAU;gCACV,WAAU;;;;;;;;;;;;kCAId,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAY,WAAU;0CAA0C;;;;;;0CAG/E,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,QAAQ,SAAS;gCACxB,UAAU;gCACV,WAAU;gCACV,KAAI;gCACJ,MAAK;;;;;;;;;;;;kCAIT,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAY,WAAU;0CAA0C;;;;;;0CAG/E,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,QAAQ,SAAS;gCACxB,UAAU;gCACV,WAAU;gCACV,KAAI;gCACJ,MAAK;;;;;;;;;;;;;;;;;;;;;;;;AAMjB;GAhJwB;KAAA", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/RDV/rdv-app/src/components/admin/PaymentList.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { format } from 'date-fns';\r\nimport { fr } from 'date-fns/locale';\r\n\r\ninterface Payment {\r\n  _id: string;\r\n  userId: {\r\n    name: string;\r\n    email: string;\r\n    businessName: string;\r\n  };\r\n  amount: number;\r\n  currency: string;\r\n  status: 'pending' | 'completed' | 'failed';\r\n  paymentMethod: string;\r\n  subscriptionPlan: string;\r\n  subscriptionPeriod: 'monthly' | 'yearly';\r\n  startDate: string;\r\n  endDate: string;\r\n  createdAt: string;\r\n}\r\n\r\ninterface PaymentListProps {\r\n  payments: Payment[];\r\n}\r\n\r\nexport default function PaymentList({ payments: initialPayments }: PaymentListProps) {\r\n  const [payments, setPayments] = useState(initialPayments);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const handleStatusUpdate = async (paymentId: string, newStatus: 'completed' | 'failed') => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await fetch(`/api/admin/payments/${paymentId}/status`, {\r\n        method: 'PATCH',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ status: newStatus }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to update payment status');\r\n      }\r\n\r\n      setPayments(payments.map(payment => \r\n        payment._id === paymentId \r\n          ? { ...payment, status: newStatus }\r\n          : payment\r\n      ));\r\n    } catch (error) {\r\n      console.error('Error updating payment status:', error);\r\n      alert('Failed to update payment status');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleExportCSV = () => {\r\n    const headers = [\r\n      'Commerce',\r\n      'Email',\r\n      'Montant',\r\n      'Devise',\r\n      'Statut',\r\n      'Méthode de paiement',\r\n      'Plan',\r\n      'Période',\r\n      'Date de début',\r\n      'Date de fin',\r\n      'Date de création'\r\n    ];\r\n\r\n    const csvData = payments.map(payment => [\r\n      payment.userId.businessName,\r\n      payment.userId.email,\r\n      payment.amount.toString(),\r\n      payment.currency,\r\n      payment.status,\r\n      payment.paymentMethod,\r\n      payment.subscriptionPlan,\r\n      payment.subscriptionPeriod,\r\n      format(new Date(payment.startDate), 'dd/MM/yyyy'),\r\n      format(new Date(payment.endDate), 'dd/MM/yyyy'),\r\n      format(new Date(payment.createdAt), 'dd/MM/yyyy')\r\n    ]);\r\n\r\n    const csvContent = [\r\n      headers.join(','),\r\n      ...csvData.map(row => row.join(','))\r\n    ].join('\\n');\r\n\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    const url = URL.createObjectURL(blob);\r\n    link.setAttribute('href', url);\r\n    link.setAttribute('download', `paiements_${format(new Date(), 'yyyy-MM-dd')}.csv`);\r\n    link.style.visibility = 'hidden';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  };\r\n\r\n  const formatDate = (date: string) => {\r\n    return format(new Date(date), 'dd MMMM yyyy', { locale: fr });\r\n  };\r\n\r\n  const formatAmount = (amount: number, currency: string) => {\r\n    return new Intl.NumberFormat('fr-FR', {\r\n      style: 'currency',\r\n      currency: currency,\r\n    }).format(amount);\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'completed':\r\n        return 'bg-green-100 text-green-800';\r\n      case 'failed':\r\n        return 'bg-red-100 text-red-800';\r\n      default:\r\n        return 'bg-yellow-100 text-yellow-800';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-end mb-4\">\r\n        <button\r\n          onClick={handleExportCSV}\r\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n        >\r\n          Exporter en CSV\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"overflow-x-auto\">\r\n        <table className=\"min-w-full divide-y divide-gray-200\">\r\n          <thead className=\"bg-gray-50\">\r\n            <tr>\r\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                Commerce\r\n              </th>\r\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                Montant\r\n              </th>\r\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                Plan\r\n              </th>\r\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                Période\r\n              </th>\r\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                Statut\r\n              </th>\r\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                Date\r\n              </th>\r\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                Actions\r\n              </th>\r\n            </tr>\r\n          </thead>\r\n          <tbody className=\"bg-white divide-y divide-gray-200\">\r\n            {payments.map((payment) => (\r\n              <tr key={payment._id}>\r\n                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                  <div className=\"text-sm font-medium text-gray-900\">\r\n                    {payment.userId.businessName}\r\n                  </div>\r\n                  <div className=\"text-sm text-gray-500\">{payment.userId.email}</div>\r\n                </td>\r\n                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                  <div className=\"text-sm text-gray-900\">\r\n                    {formatAmount(payment.amount, payment.currency)}\r\n                  </div>\r\n                </td>\r\n                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                  <div className=\"text-sm text-gray-900\">{payment.subscriptionPlan}</div>\r\n                </td>\r\n                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                  <div className=\"text-sm text-gray-900\">\r\n                    {payment.subscriptionPeriod === 'monthly' ? 'Mensuel' : 'Annuel'}\r\n                  </div>\r\n                </td>\r\n                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(payment.status)}`}>\r\n                    {payment.status === 'completed' ? 'Complété' : \r\n                     payment.status === 'failed' ? 'Échoué' : 'En attente'}\r\n                  </span>\r\n                </td>\r\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                  {formatDate(payment.createdAt)}\r\n                </td>\r\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n                  {payment.status === 'pending' && (\r\n                    <div className=\"space-x-2\">\r\n                      <button\r\n                        onClick={() => handleStatusUpdate(payment._id, 'completed')}\r\n                        disabled={loading}\r\n                        className=\"text-green-600 hover:text-green-900\"\r\n                      >\r\n                        Valider\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleStatusUpdate(payment._id, 'failed')}\r\n                        disabled={loading}\r\n                        className=\"text-red-600 hover:text-red-900\"\r\n                      >\r\n                        Rejeter\r\n                      </button>\r\n                    </div>\r\n                  )}\r\n                </td>\r\n              </tr>\r\n            ))}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AA4Be,SAAS,YAAY,EAAE,UAAU,eAAe,EAAoB;;IACjF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,qBAAqB,OAAO,WAAmB;QACnD,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,UAAU,OAAO,CAAC,EAAE;gBACtE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ;gBAAU;YAC3C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,YAAY,SAAS,GAAG,CAAC,CAAA,UACvB,QAAQ,GAAG,KAAK,YACZ;oBAAE,GAAG,OAAO;oBAAE,QAAQ;gBAAU,IAChC;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,UAAU;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,UAAU,SAAS,GAAG,CAAC,CAAA,UAAW;gBACtC,QAAQ,MAAM,CAAC,YAAY;gBAC3B,QAAQ,MAAM,CAAC,KAAK;gBACpB,QAAQ,MAAM,CAAC,QAAQ;gBACvB,QAAQ,QAAQ;gBAChB,QAAQ,MAAM;gBACd,QAAQ,aAAa;gBACrB,QAAQ,gBAAgB;gBACxB,QAAQ,kBAAkB;gBAC1B,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,SAAS,GAAG;gBACpC,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,OAAO,GAAG;gBAClC,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,SAAS,GAAG;aACrC;QAED,MAAM,aAAa;YACjB,QAAQ,IAAI,CAAC;eACV,QAAQ,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC;SAChC,CAAC,IAAI,CAAC;QAEP,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAA0B;QACtE,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,KAAK,YAAY,CAAC,QAAQ;QAC1B,KAAK,YAAY,CAAC,YAAY,CAAC,UAAU,EAAE,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ,cAAc,IAAI,CAAC;QACjF,KAAK,KAAK,CAAC,UAAU,GAAG;QACxB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,gBAAgB;YAAE,QAAQ,8IAAA,CAAA,KAAE;QAAC;IAC7D;IAEA,MAAM,eAAe,CAAC,QAAgB;QACpC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;0BAKH,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;;;;;;;;;;;;sCAKnG,6LAAC;4BAAM,WAAU;sCACd,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,MAAM,CAAC,YAAY;;;;;;8DAE9B,6LAAC;oDAAI,WAAU;8DAAyB,QAAQ,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAE9D,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;0DACZ,aAAa,QAAQ,MAAM,EAAE,QAAQ,QAAQ;;;;;;;;;;;sDAGlD,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;0DAAyB,QAAQ,gBAAgB;;;;;;;;;;;sDAElE,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,kBAAkB,KAAK,YAAY,YAAY;;;;;;;;;;;sDAG5D,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAK,WAAW,CAAC,8DAA8D,EAAE,eAAe,QAAQ,MAAM,GAAG;0DAC/G,QAAQ,MAAM,KAAK,cAAc,aACjC,QAAQ,MAAM,KAAK,WAAW,WAAW;;;;;;;;;;;sDAG9C,6LAAC;4CAAG,WAAU;sDACX,WAAW,QAAQ,SAAS;;;;;;sDAE/B,6LAAC;4CAAG,WAAU;sDACX,QAAQ,MAAM,KAAK,2BAClB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,mBAAmB,QAAQ,GAAG,EAAE;wDAC/C,UAAU;wDACV,WAAU;kEACX;;;;;;kEAGD,6LAAC;wDACC,SAAS,IAAM,mBAAmB,QAAQ,GAAG,EAAE;wDAC/C,UAAU;wDACV,WAAU;kEACX;;;;;;;;;;;;;;;;;;mCA3CA,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDlC;GAnMwB;KAAA", "debugId": null}}, {"offset": {"line": 871, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/RDV/rdv-app/src/components/admin/PaymentStats.tsx"], "sourcesContent": ["import { format } from 'date-fns';\r\nimport { fr } from 'date-fns/locale';\r\n\r\ninterface PaymentStatsProps {\r\n  stats: {\r\n    totalAmount: number;\r\n    totalCount: number;\r\n    byStatus: {\r\n      pending: { count: number; amount: number };\r\n      completed: { count: number; amount: number };\r\n      failed: { count: number; amount: number };\r\n    };\r\n    byPeriod: {\r\n      monthly: { count: number; amount: number };\r\n      yearly: { count: number; amount: number };\r\n    };\r\n    byPlan: {\r\n      [key: string]: { count: number; amount: number };\r\n    };\r\n  };\r\n}\r\n\r\nexport default function PaymentStats({ stats }: PaymentStatsProps) {\r\n  const formatAmount = (amount: number) => {\r\n    return new Intl.NumberFormat('fr-FR', {\r\n      style: 'currency',\r\n      currency: 'EUR',\r\n    }).format(amount);\r\n  };\r\n\r\n  // Protection contre les données manquantes\r\n  const safeStats = {\r\n    totalAmount: stats?.totalAmount || 0,\r\n    totalCount: stats?.totalCount || 0,\r\n    byStatus: {\r\n      pending: stats?.byStatus?.pending || { count: 0, amount: 0 },\r\n      completed: stats?.byStatus?.completed || { count: 0, amount: 0 },\r\n      failed: stats?.byStatus?.failed || { count: 0, amount: 0 }\r\n    },\r\n    byPeriod: {\r\n      monthly: stats?.byPeriod?.monthly || { count: 0, amount: 0 },\r\n      yearly: stats?.byPeriod?.yearly || { count: 0, amount: 0 }\r\n    },\r\n    byPlan: stats?.byPlan || {}\r\n  };\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n      {/* Statistiques générales */}\r\n      <div className=\"bg-gradient-to-br from-blue-50 to-indigo-100 p-6 rounded-xl shadow-lg border border-blue-200\">\r\n        <div className=\"flex items-center justify-between mb-4\">\r\n          <h3 className=\"text-lg font-semibold text-blue-900\">Statistiques générales</h3>\r\n          <div className=\"p-2 bg-blue-500 rounded-lg\">\r\n            <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n        <div className=\"space-y-3\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"text-blue-700 font-medium\">Total des paiements</span>\r\n            <span className=\"text-2xl font-bold text-blue-900 bg-white px-3 py-1 rounded-lg shadow-sm\">\r\n              {safeStats.totalCount}\r\n            </span>\r\n          </div>\r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"text-blue-700 font-medium\">Montant total</span>\r\n            <span className=\"text-2xl font-bold text-green-600 bg-white px-3 py-1 rounded-lg shadow-sm\">\r\n              {formatAmount(safeStats.totalAmount)}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Statistiques par statut */}\r\n      <div className=\"bg-gradient-to-br from-green-50 to-emerald-100 p-6 rounded-xl shadow-lg border border-green-200\">\r\n        <div className=\"flex items-center justify-between mb-4\">\r\n          <h3 className=\"text-lg font-semibold text-green-900\">Par statut</h3>\r\n          <div className=\"p-2 bg-green-500 rounded-lg\">\r\n            <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n        <div className=\"space-y-3\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"text-yellow-700 font-medium\">En attente</span>\r\n            <div className=\"text-right\">\r\n              <div className=\"text-lg font-bold text-yellow-600\">{safeStats.byStatus.pending.count}</div>\r\n              <div className=\"text-sm font-semibold text-yellow-800 bg-yellow-100 px-2 py-1 rounded\">\r\n                {formatAmount(safeStats.byStatus.pending.amount)}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"text-green-700 font-medium\">Complétés</span>\r\n            <div className=\"text-right\">\r\n              <div className=\"text-lg font-bold text-green-600\">{safeStats.byStatus.completed.count}</div>\r\n              <div className=\"text-sm font-semibold text-green-800 bg-green-100 px-2 py-1 rounded\">\r\n                {formatAmount(safeStats.byStatus.completed.amount)}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"text-red-700 font-medium\">Échoués</span>\r\n            <div className=\"text-right\">\r\n              <div className=\"text-lg font-bold text-red-600\">{safeStats.byStatus.failed.count}</div>\r\n              <div className=\"text-sm font-semibold text-red-800 bg-red-100 px-2 py-1 rounded\">\r\n                {formatAmount(safeStats.byStatus.failed.amount)}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Statistiques par période */}\r\n      <div className=\"bg-gradient-to-br from-purple-50 to-violet-100 p-6 rounded-xl shadow-lg border border-purple-200\">\r\n        <div className=\"flex items-center justify-between mb-4\">\r\n          <h3 className=\"text-lg font-semibold text-purple-900\">Par période</h3>\r\n          <div className=\"p-2 bg-purple-500 rounded-lg\">\r\n            <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n        <div className=\"space-y-3\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"text-purple-700 font-medium\">Mensuel</span>\r\n            <div className=\"text-right\">\r\n              <div className=\"text-lg font-bold text-purple-600\">{safeStats.byPeriod.monthly.count}</div>\r\n              <div className=\"text-sm font-semibold text-purple-800 bg-purple-100 px-2 py-1 rounded\">\r\n                {formatAmount(safeStats.byPeriod.monthly.amount)}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"text-purple-700 font-medium\">Annuel</span>\r\n            <div className=\"text-right\">\r\n              <div className=\"text-lg font-bold text-purple-600\">{safeStats.byPeriod.yearly.count}</div>\r\n              <div className=\"text-sm font-semibold text-purple-800 bg-purple-100 px-2 py-1 rounded\">\r\n                {formatAmount(safeStats.byPeriod.yearly.amount)}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Statistiques par plan */}\r\n      <div className=\"bg-gradient-to-br from-orange-50 to-amber-100 p-6 rounded-xl shadow-lg border border-orange-200 lg:col-span-3\">\r\n        <div className=\"flex items-center justify-between mb-6\">\r\n          <h3 className=\"text-xl font-semibold text-orange-900\">Par plan d'abonnement</h3>\r\n          <div className=\"p-2 bg-orange-500 rounded-lg\">\r\n            <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n          {Object.entries(safeStats.byPlan).map(([plan, data], index) => {\r\n            const colors = [\r\n              'from-rose-100 to-pink-200 border-rose-300',\r\n              'from-cyan-100 to-blue-200 border-cyan-300',\r\n              'from-emerald-100 to-green-200 border-emerald-300',\r\n              'from-violet-100 to-purple-200 border-violet-300',\r\n              'from-amber-100 to-yellow-200 border-amber-300'\r\n            ];\r\n            const textColors = [\r\n              'text-rose-800',\r\n              'text-cyan-800',\r\n              'text-emerald-800',\r\n              'text-violet-800',\r\n              'text-amber-800'\r\n            ];\r\n            const bgColors = [\r\n              'bg-rose-50',\r\n              'bg-cyan-50',\r\n              'bg-emerald-50',\r\n              'bg-violet-50',\r\n              'bg-amber-50'\r\n            ];\r\n\r\n            return (\r\n              <div key={plan} className={`bg-gradient-to-br ${colors[index % colors.length]} p-5 rounded-xl shadow-md border`}>\r\n                <h4 className={`font-bold text-lg ${textColors[index % textColors.length]} mb-3 capitalize`}>\r\n                  {plan}\r\n                </h4>\r\n                <div className=\"space-y-3\">\r\n                  <div className={`${bgColors[index % bgColors.length]} p-3 rounded-lg`}>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <span className={`${textColors[index % textColors.length]} font-medium`}>Nombre</span>\r\n                      <span className={`text-xl font-bold ${textColors[index % textColors.length]}`}>\r\n                        {data.count}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                  <div className={`${bgColors[index % bgColors.length]} p-3 rounded-lg`}>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <span className={`${textColors[index % textColors.length]} font-medium`}>Montant</span>\r\n                      <span className={`text-lg font-bold ${textColors[index % textColors.length]}`}>\r\n                        {formatAmount(data.amount)}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;;AAsBe,SAAS,aAAa,EAAE,KAAK,EAAqB;IAC/D,MAAM,eAAe,CAAC;QACpB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,2CAA2C;IAC3C,MAAM,YAAY;QAChB,aAAa,OAAO,eAAe;QACnC,YAAY,OAAO,cAAc;QACjC,UAAU;YACR,SAAS,OAAO,UAAU,WAAW;gBAAE,OAAO;gBAAG,QAAQ;YAAE;YAC3D,WAAW,OAAO,UAAU,aAAa;gBAAE,OAAO;gBAAG,QAAQ;YAAE;YAC/D,QAAQ,OAAO,UAAU,UAAU;gBAAE,OAAO;gBAAG,QAAQ;YAAE;QAC3D;QACA,UAAU;YACR,SAAS,OAAO,UAAU,WAAW;gBAAE,OAAO;gBAAG,QAAQ;YAAE;YAC3D,QAAQ,OAAO,UAAU,UAAU;gBAAE,OAAO;gBAAG,QAAQ;YAAE;QAC3D;QACA,QAAQ,OAAO,UAAU,CAAC;IAC5B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAqB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC5E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAI3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA4B;;;;;;kDAC5C,6LAAC;wCAAK,WAAU;kDACb,UAAU,UAAU;;;;;;;;;;;;0CAGzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA4B;;;;;;kDAC5C,6LAAC;wCAAK,WAAU;kDACb,aAAa,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAqB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC5E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAI3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA8B;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAqC,UAAU,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;0DACpF,6LAAC;gDAAI,WAAU;0DACZ,aAAa,UAAU,QAAQ,CAAC,OAAO,CAAC,MAAM;;;;;;;;;;;;;;;;;;0CAIrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;kDAC7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoC,UAAU,QAAQ,CAAC,SAAS,CAAC,KAAK;;;;;;0DACrF,6LAAC;gDAAI,WAAU;0DACZ,aAAa,UAAU,QAAQ,CAAC,SAAS,CAAC,MAAM;;;;;;;;;;;;;;;;;;0CAIvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2B;;;;;;kDAC3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAkC,UAAU,QAAQ,CAAC,MAAM,CAAC,KAAK;;;;;;0DAChF,6LAAC;gDAAI,WAAU;0DACZ,aAAa,UAAU,QAAQ,CAAC,MAAM,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAqB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC5E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAI3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA8B;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAqC,UAAU,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;0DACpF,6LAAC;gDAAI,WAAU;0DACZ,aAAa,UAAU,QAAQ,CAAC,OAAO,CAAC,MAAM;;;;;;;;;;;;;;;;;;0CAIrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA8B;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAqC,UAAU,QAAQ,CAAC,MAAM,CAAC,KAAK;;;;;;0DACnF,6LAAC;gDAAI,WAAU;0DACZ,aAAa,UAAU,QAAQ,CAAC,MAAM,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAqB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC5E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAI3E,6LAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,UAAU,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,EAAE;4BACnD,MAAM,SAAS;gCACb;gCACA;gCACA;gCACA;gCACA;6BACD;4BACD,MAAM,aAAa;gCACjB;gCACA;gCACA;gCACA;gCACA;6BACD;4BACD,MAAM,WAAW;gCACf;gCACA;gCACA;gCACA;gCACA;6BACD;4BAED,qBACE,6LAAC;gCAAe,WAAW,CAAC,kBAAkB,EAAE,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC,CAAC,gCAAgC,CAAC;;kDAC7G,6LAAC;wCAAG,WAAW,CAAC,kBAAkB,EAAE,UAAU,CAAC,QAAQ,WAAW,MAAM,CAAC,CAAC,gBAAgB,CAAC;kDACxF;;;;;;kDAEH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,GAAG,QAAQ,CAAC,QAAQ,SAAS,MAAM,CAAC,CAAC,eAAe,CAAC;0DACnE,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,GAAG,UAAU,CAAC,QAAQ,WAAW,MAAM,CAAC,CAAC,YAAY,CAAC;sEAAE;;;;;;sEACzE,6LAAC;4DAAK,WAAW,CAAC,kBAAkB,EAAE,UAAU,CAAC,QAAQ,WAAW,MAAM,CAAC,EAAE;sEAC1E,KAAK,KAAK;;;;;;;;;;;;;;;;;0DAIjB,6LAAC;gDAAI,WAAW,GAAG,QAAQ,CAAC,QAAQ,SAAS,MAAM,CAAC,CAAC,eAAe,CAAC;0DACnE,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,GAAG,UAAU,CAAC,QAAQ,WAAW,MAAM,CAAC,CAAC,YAAY,CAAC;sEAAE;;;;;;sEACzE,6LAAC;4DAAK,WAAW,CAAC,kBAAkB,EAAE,UAAU,CAAC,QAAQ,WAAW,MAAM,CAAC,EAAE;sEAC1E,aAAa,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;+BAjBzB;;;;;wBAwBd;;;;;;;;;;;;;;;;;;AAKV;KA7LwB", "debugId": null}}, {"offset": {"line": 1554, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/RDV/rdv-app/src/components/admin/PaymentsManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport PaymentFilters from './PaymentFilters';\nimport PaymentList from './PaymentList';\nimport PaymentStats from './PaymentStats';\n\ninterface Payment {\n  _id: string;\n  userId: {\n    name: string;\n    email: string;\n    businessName: string;\n  };\n  amount: number;\n  currency: string;\n  status: 'pending' | 'completed' | 'failed';\n  paymentMethod: string;\n  subscriptionPlan: string;\n  subscriptionPeriod: 'monthly' | 'yearly';\n  startDate: string;\n  endDate: string;\n  createdAt: string;\n}\n\ninterface PaymentsManagerProps {\n  initialPayments: Payment[];\n  initialStats: any;\n}\n\nexport default function PaymentsManager({ initialPayments, initialStats }: PaymentsManagerProps) {\n  const [payments, setPayments] = useState<Payment[]>(initialPayments);\n  const [stats, setStats] = useState(initialStats);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleFilterChange = async (filters: {\n    status?: string;\n    startDate?: string;\n    endDate?: string;\n    minAmount?: number;\n    maxAmount?: number;\n  }) => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Construire les paramètres de requête\n      const params = new URLSearchParams();\n      \n      if (filters.status) params.append('status', filters.status);\n      if (filters.startDate) params.append('startDate', filters.startDate);\n      if (filters.endDate) params.append('endDate', filters.endDate);\n      if (filters.minAmount !== undefined) params.append('minAmount', filters.minAmount.toString());\n      if (filters.maxAmount !== undefined) params.append('maxAmount', filters.maxAmount.toString());\n\n      const response = await fetch(`/api/admin/payments?${params.toString()}`);\n      \n      if (!response.ok) {\n        throw new Error('Erreur lors du chargement des paiements');\n      }\n\n      const data = await response.json();\n      setPayments(data.payments);\n\n      // Recalculer les statistiques pour les données filtrées\n      const filteredStats = calculateStats(data.payments);\n      setStats(filteredStats);\n\n    } catch (err: any) {\n      console.error('Erreur lors du filtrage:', err);\n      setError(err.message || 'Erreur lors du filtrage des paiements');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateStats = (paymentsData: Payment[]) => {\n    const totalAmount = paymentsData.reduce((sum, payment) => sum + payment.amount, 0);\n    const totalCount = paymentsData.length;\n\n    const byStatus = {\n      pending: { count: 0, amount: 0 },\n      completed: { count: 0, amount: 0 },\n      failed: { count: 0, amount: 0 }\n    };\n\n    const byPeriod = {\n      monthly: { count: 0, amount: 0 },\n      yearly: { count: 0, amount: 0 }\n    };\n\n    const byPlan: any = {};\n\n    paymentsData.forEach(payment => {\n      // Statistiques par statut\n      if (byStatus[payment.status]) {\n        byStatus[payment.status].count++;\n        byStatus[payment.status].amount += payment.amount;\n      }\n\n      // Statistiques par période\n      if (byPeriod[payment.subscriptionPeriod]) {\n        byPeriod[payment.subscriptionPeriod].count++;\n        byPeriod[payment.subscriptionPeriod].amount += payment.amount;\n      }\n\n      // Statistiques par plan\n      if (!byPlan[payment.subscriptionPlan]) {\n        byPlan[payment.subscriptionPlan] = { count: 0, amount: 0 };\n      }\n      byPlan[payment.subscriptionPlan].count++;\n      byPlan[payment.subscriptionPlan].amount += payment.amount;\n    });\n\n    return {\n      totalAmount,\n      totalCount,\n      byStatus,\n      byPeriod,\n      byPlan\n    };\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Statistiques */}\n      <PaymentStats stats={stats} />\n\n      {/* Filtres */}\n      <div className=\"relative\">\n        <PaymentFilters onFilterChange={handleFilterChange} />\n        {loading && (\n          <div className=\"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"></div>\n              <span className=\"text-sm text-gray-600\">Filtrage en cours...</span>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Messages d'erreur */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-red-800\">{error}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Résultats */}\n      <div className=\"bg-white rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">\n            Paiements ({payments.length} résultat{payments.length !== 1 ? 's' : ''})\n          </h3>\n        </div>\n        <PaymentList payments={payments} />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AA8Be,SAAS,gBAAgB,EAAE,eAAe,EAAE,YAAY,EAAwB;;IAC7F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,qBAAqB,OAAO;QAOhC,IAAI;YACF,WAAW;YACX,SAAS;YAET,uCAAuC;YACvC,MAAM,SAAS,IAAI;YAEnB,IAAI,QAAQ,MAAM,EAAE,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YAC1D,IAAI,QAAQ,SAAS,EAAE,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;YACnE,IAAI,QAAQ,OAAO,EAAE,OAAO,MAAM,CAAC,WAAW,QAAQ,OAAO;YAC7D,IAAI,QAAQ,SAAS,KAAK,WAAW,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS,CAAC,QAAQ;YAC1F,IAAI,QAAQ,SAAS,KAAK,WAAW,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS,CAAC,QAAQ;YAE1F,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,OAAO,QAAQ,IAAI;YAEvE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY,KAAK,QAAQ;YAEzB,wDAAwD;YACxD,MAAM,gBAAgB,eAAe,KAAK,QAAQ;YAClD,SAAS;QAEX,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc,aAAa,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE;QAChF,MAAM,aAAa,aAAa,MAAM;QAEtC,MAAM,WAAW;YACf,SAAS;gBAAE,OAAO;gBAAG,QAAQ;YAAE;YAC/B,WAAW;gBAAE,OAAO;gBAAG,QAAQ;YAAE;YACjC,QAAQ;gBAAE,OAAO;gBAAG,QAAQ;YAAE;QAChC;QAEA,MAAM,WAAW;YACf,SAAS;gBAAE,OAAO;gBAAG,QAAQ;YAAE;YAC/B,QAAQ;gBAAE,OAAO;gBAAG,QAAQ;YAAE;QAChC;QAEA,MAAM,SAAc,CAAC;QAErB,aAAa,OAAO,CAAC,CAAA;YACnB,0BAA0B;YAC1B,IAAI,QAAQ,CAAC,QAAQ,MAAM,CAAC,EAAE;gBAC5B,QAAQ,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK;gBAC9B,QAAQ,CAAC,QAAQ,MAAM,CAAC,CAAC,MAAM,IAAI,QAAQ,MAAM;YACnD;YAEA,2BAA2B;YAC3B,IAAI,QAAQ,CAAC,QAAQ,kBAAkB,CAAC,EAAE;gBACxC,QAAQ,CAAC,QAAQ,kBAAkB,CAAC,CAAC,KAAK;gBAC1C,QAAQ,CAAC,QAAQ,kBAAkB,CAAC,CAAC,MAAM,IAAI,QAAQ,MAAM;YAC/D;YAEA,wBAAwB;YACxB,IAAI,CAAC,MAAM,CAAC,QAAQ,gBAAgB,CAAC,EAAE;gBACrC,MAAM,CAAC,QAAQ,gBAAgB,CAAC,GAAG;oBAAE,OAAO;oBAAG,QAAQ;gBAAE;YAC3D;YACA,MAAM,CAAC,QAAQ,gBAAgB,CAAC,CAAC,KAAK;YACtC,MAAM,CAAC,QAAQ,gBAAgB,CAAC,CAAC,MAAM,IAAI,QAAQ,MAAM;QAC3D;QAEA,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,8IAAA,CAAA,UAAY;gBAAC,OAAO;;;;;;0BAGrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gJAAA,CAAA,UAAc;wBAAC,gBAAgB;;;;;;oBAC/B,yBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;YAO/C,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAuB,SAAQ;gCAAY,MAAK;0CAC7D,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;;;;;;;sCAGlQ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCAAoC;gCACpC,SAAS,MAAM;gCAAC;gCAAU,SAAS,MAAM,KAAK,IAAI,MAAM;gCAAG;;;;;;;;;;;;kCAG3E,6LAAC,6IAAA,CAAA,UAAW;wBAAC,UAAU;;;;;;;;;;;;;;;;;;AAI/B;GA3IwB;KAAA", "debugId": null}}]}