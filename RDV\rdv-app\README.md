# 📅 Planidoo - Plateforme de Gestion de Rendez-vous

**Planidoo** est une solution moderne et intuitive pour gérer les rendez-vous en ligne. Conçue pour les petits commerces, elle offre une interface élégante avec des dégradés colorés et une expérience utilisateur exceptionnelle.

## ✨ Fonctionnalités

- 🎨 **Design moderne** avec dégradés colorés et animations
- 📱 **Interface responsive** adaptée à tous les appareils
- 👥 **Gestion multi-utilisateurs** (clients, commerces, administrateurs)
- 📅 **Système de réservation** en temps réel
- 💳 **Gestion des abonnements** avec paiements intégrés
- 🏪 **Sites vitrines** personnalisables pour chaque commerce
- 📊 **Tableau de bord** avec statistiques avancées
- 🔐 **Authentification sécurisée** avec NextAuth.js

## 🚀 Démarrage rapide

### Prérequis
- Node.js 18+
- MongoDB
- npm ou yarn

### Installation

```bash
# Cloner le projet
git clone [url-du-repo]
cd planidoo

# Installer les dépendances
npm install

# Configurer les variables d'environnement
cp .env.example .env.local

# Lancer le serveur de développement
npm run dev
```

Ouvrez [http://localhost:3000](http://localhost:3000) dans votre navigateur.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
