import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import DashboardNav from '@/components/DashboardNav';
import VitrineManager from '@/components/VitrineManager';

async function getUserVitrineData(userId: string) {
  await connectDB();
  const user = await User.findById(userId).select(
    'businessName businessDescription businessImages socialMedia coordinates businessCategory businessTags isVitrineActive phone address services'
  );

  if (!user) {
    return null;
  }

  // Sérialiser complètement les données pour éviter les erreurs de passage d'objets MongoDB
  const userData = JSON.parse(JSON.stringify(user.toObject()));

  return {
    _id: userData._id.toString(),
    businessName: userData.businessName || '',
    businessDescription: userData.businessDescription || '',
    businessImages: Array.isArray(userData.businessImages) ? userData.businessImages : [],
    socialMedia: {
      facebook: userData.socialMedia?.facebook || '',
      instagram: userData.socialMedia?.instagram || '',
      twitter: userData.socialMedia?.twitter || '',
      linkedin: userData.socialMedia?.linkedin || '',
      website: userData.socialMedia?.website || '',
    },
    coordinates: {
      latitude: userData.coordinates?.latitude || 0,
      longitude: userData.coordinates?.longitude || 0,
    },
    businessCategory: userData.businessCategory || '',
    businessTags: Array.isArray(userData.businessTags) ? userData.businessTags : [],
    isVitrineActive: Boolean(userData.isVitrineActive),
    phone: userData.phone || '',
    address: userData.address || '',
    services: Array.isArray(userData.services) ? userData.services : [],
  };
}

export default async function VitrinePage() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    redirect('/auth/signin');
  }

  const vitrineData = await getUserVitrineData(session.user.id);

  if (!vitrineData) {
    redirect('/auth/signin');
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardNav />
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Site vitrine</h1>
            <p className="mt-2 text-gray-600">
              Gérez votre site vitrine pour présenter votre commerce au public
            </p>
            {vitrineData.isVitrineActive && (
              <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-800">
                  🎉 Votre site vitrine est actif ! 
                  <a 
                    href={`/vitrine/${vitrineData._id}`}
                    target="_blank"
                    className="ml-2 text-green-600 hover:text-green-800 underline"
                  >
                    Voir votre vitrine →
                  </a>
                </p>
              </div>
            )}
          </div>

          <VitrineManager vitrineData={vitrineData} />
        </div>
      </div>
    </div>
  );
}
