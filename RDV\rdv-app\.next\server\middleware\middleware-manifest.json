{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/bookings/:path*{(\\\\.json)}?", "originalSource": "/api/bookings/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/settings/:path*{(\\\\.json)}?", "originalSource": "/api/settings/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/admin/:path*{(\\\\.json)}?", "originalSource": "/api/admin/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mJUVSA/qWlVKu7Kgb2AsW3tppVIB2gGv6Re9K3RWEWg=", "__NEXT_PREVIEW_MODE_ID": "8a2011205c7e0721eec6acdedbb7fdf5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ce6d6ddd4d7e839155f63289a0cd54e4d1b6793ef3a815dd1fdc1c1ccfa57ae0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "24533729f24a15f9fd75da8eb6877bb68754e105d05149048d0fb9441587bac0"}}}, "instrumentation": null, "functions": {}}