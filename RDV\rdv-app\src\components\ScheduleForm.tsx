'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';

const DAYS = [
  { id: 'monday', label: '<PERSON><PERSON>' },
  { id: 'tuesday', label: 'Mar<PERSON>' },
  { id: 'wednesday', label: 'Mercredi' },
  { id: 'thursday', label: '<PERSON><PERSON>' },
  { id: 'friday', label: 'Vendredi' },
  { id: 'saturday', label: '<PERSON>di' },
  { id: 'sunday', label: 'Dimanche' },
];

interface ScheduleDay {
  isOpen: boolean;
  start: string;
  end: string;
}

interface Schedule {
  [key: string]: ScheduleDay;
}

interface ScheduleFormProps {
  initialSchedule: Schedule;
}

export default function ScheduleForm({ initialSchedule }: ScheduleFormProps) {
  const [schedule, setSchedule] = useState<Schedule>(initialSchedule);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleToggleDay = (dayId: string) => {
    setSchedule((prev) => ({
      ...prev,
      [dayId]: {
        ...prev[dayId],
        isOpen: !prev[dayId].isOpen,
      },
    }));
  };

  const handleTimeChange = (dayId: string, field: 'start' | 'end', value: string) => {
    setSchedule((prev) => ({
      ...prev,
      [dayId]: {
        ...prev[dayId],
        [field]: value,
      },
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/settings/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(schedule),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erreur lors de la sauvegarde des horaires');
      }

      setMessage('Horaires sauvegardés avec succès');
    } catch (error: any) {
      console.error('Erreur:', error);
      setMessage(error.message || 'Erreur lors de la sauvegarde des horaires');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {message && (
        <div className={`p-4 rounded-md ${
          message.includes('succès') ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
        }`}>
          {message}
        </div>
      )}

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Horaires de travail</CardTitle>
            <Button onClick={handleSave} disabled={isLoading}>
              {isLoading ? 'Enregistrement...' : 'Enregistrer les modifications'}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {DAYS.map((day) => (
              <div key={day.id} className="flex items-center space-x-4">
                <div className="w-32">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={schedule[day.id]?.isOpen || false}
                      onChange={() => handleToggleDay(day.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-700">
                      {day.label}
                    </span>
                  </label>
                </div>
                {schedule[day.id]?.isOpen && (
                  <div className="flex items-center space-x-2">
                    <input
                      type="time"
                      value={schedule[day.id]?.start || '09:00'}
                      onChange={(e) => handleTimeChange(day.id, 'start', e.target.value)}
                      className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                    <span className="text-gray-500">à</span>
                    <input
                      type="time"
                      value={schedule[day.id]?.end || '18:00'}
                      onChange={(e) => handleTimeChange(day.id, 'end', e.target.value)}
                      className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
