import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import Booking from '@/models/Booking';
import User from '@/models/User';

export async function POST(req: Request) {
  try {
    const { userId, clientName, clientEmail, date, time, service, notes } = await req.json();

    if (!userId || !clientName || !clientEmail || !date || !time) {
      return NextResponse.json(
        { message: 'Tous les champs obligatoires doivent être remplis' },
        { status: 400 }
      );
    }

    await connectDB();

    // Vérifier que le commerce existe et est vérifié
    const business = await User.findById(userId);
    if (!business || !business.isVerified) {
      return NextResponse.json(
        { message: 'Commerce non trouvé ou non vérifié' },
        { status: 404 }
      );
    }

    // C<PERSON>er ou trouver le client
    let client = await User.findOne({ email: clientEmail });
    if (!client) {
      // Créer un compte client temporaire
      client = await User.create({
        name: clientName,
        email: clientEmail,
        businessName: 'Client',
        password: 'temp_password', // Mot de passe temporaire
        isVerified: true,
      });
    }

    // Vérifier la disponibilité du créneau
    const bookingDate = new Date(date);
    const existingBooking = await Booking.findOne({
      businessId: userId,
      date: bookingDate,
      time: time,
      status: { $in: ['pending', 'confirmed'] }
    });

    if (existingBooking) {
      return NextResponse.json(
        { message: 'Ce créneau n\'est plus disponible' },
        { status: 409 }
      );
    }

    // Créer la réservation
    const booking = await Booking.create({
      clientId: client._id,
      businessId: userId,
      service: service || 'Rendez-vous',
      date: bookingDate,
      time: time,
      status: 'pending',
      notes: notes || '',
      duration: 60,
      price: 0,
    });

    return NextResponse.json({
      message: 'Réservation créée avec succès',
      booking: {
        _id: booking._id.toString(),
        service: booking.service,
        date: booking.date.toISOString(),
        time: booking.time,
        status: booking.status,
        businessName: business.businessName,
      }
    }, { status: 201 });
  } catch (error: any) {
    console.error('Erreur lors de la création de la réservation:', error);
    return NextResponse.json(
      { message: 'Une erreur est survenue lors de la création de la réservation' },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { message: 'Non autorisé' },
        { status: 401 }
      );
    }

    await connectDB();

    const bookings = await Booking.find({ businessId: session.user.id })
      .populate('clientId', 'name email')
      .sort({ date: -1 });

    const serializedBookings = bookings.map(booking => ({
      _id: booking._id.toString(),
      clientId: {
        name: booking.clientId?.name || 'Client supprimé',
        email: booking.clientId?.email || '',
      },
      service: booking.service,
      date: booking.date.toISOString(),
      time: booking.time,
      status: booking.status,
      notes: booking.notes,
      price: booking.price,
      createdAt: booking.createdAt.toISOString(),
    }));

    return NextResponse.json({ bookings: serializedBookings });
  } catch (error: any) {
    console.error('Erreur lors de la récupération des réservations:', error);
    return NextResponse.json(
      { message: 'Erreur serveur', error: error.message },
      { status: 500 }
    );
  }
}