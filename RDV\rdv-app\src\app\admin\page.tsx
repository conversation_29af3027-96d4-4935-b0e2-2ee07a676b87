import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import AdminNav from '@/components/AdminNav';
import BusinessList from '@/components/admin/BusinessList';

async function getBusinesses() {
  await connectDB();
  const businesses = await User.find({}).select('_id name email businessName isVerified subscriptionStatus createdAt');
  return businesses.map(business => ({
    _id: business._id.toString(),
    name: business.name,
    email: business.email,
    businessName: business.businessName,
    isVerified: business.isVerified,
    subscriptionStatus: business.subscriptionStatus,
    createdAt: business.createdAt.toISOString(),
  }));
}

export default async function AdminPage() {
  const session = await getServerSession(authOptions);

  // Vérifier si l'utilisateur est admin
  if (!session?.user?.isAdmin) {
    redirect('/');
  }

  const businesses = await getBusinesses();

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminNav />
      
      <main className="py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Administration
          </h1>

          <div className="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Commerces en attente</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-blue-600">
                  {businesses.filter(b => !b.isVerified).length}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Commerces actifs</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-green-600">
                  {businesses.filter(b => b.isVerified && b.subscriptionStatus === 'active').length}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Commerces expirés</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-red-600">
                  {businesses.filter(b => b.subscriptionStatus === 'expired').length}
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="mt-8">
            <BusinessList businesses={businesses} />
          </div>
        </div>
      </main>
    </div>
  );
} 