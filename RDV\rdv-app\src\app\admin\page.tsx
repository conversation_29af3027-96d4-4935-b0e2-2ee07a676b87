import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import AdminNav from '@/components/AdminNav';
import BusinessList from '@/components/admin/BusinessList';

async function getBusinesses() {
  await connectDB();
  const businesses = await User.find({}).select('_id name email businessName isVerified subscriptionStatus createdAt');
  return businesses.map(business => ({
    _id: business._id.toString(),
    name: business.name,
    email: business.email,
    businessName: business.businessName,
    isVerified: business.isVerified,
    subscriptionStatus: business.subscriptionStatus,
    createdAt: business.createdAt.toISOString(),
  }));
}

export default async function AdminPage() {
  const session = await getServerSession(authOptions);

  // Vérifier si l'utilisateur est admin
  if (!session?.user?.isAdmin) {
    redirect('/');
  }

  const businesses = await getBusinesses();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <AdminNav />

      <main className="py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              Administration Planidoo
            </h1>
            <p className="text-gray-600 mt-2">Gérez votre plateforme et vos utilisateurs</p>
          </div>

          <div className="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <Card className="bg-gradient-to-br from-yellow-50 to-orange-100 border-yellow-200">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-yellow-500 rounded-lg">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <CardTitle className="text-yellow-900">Commerces en attente</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold text-yellow-600 bg-white bg-opacity-50 rounded-lg p-3 text-center">
                  {businesses.filter(b => !b.isVerified).length}
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-50 to-emerald-100 border-green-200">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-500 rounded-lg">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <CardTitle className="text-green-900">Commerces actifs</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold text-green-600 bg-white bg-opacity-50 rounded-lg p-3 text-center">
                  {businesses.filter(b => b.isVerified && b.subscriptionStatus === 'active').length}
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-red-50 to-rose-100 border-red-200">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-red-500 rounded-lg">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <CardTitle className="text-red-900">Commerces expirés</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold text-red-600 bg-white bg-opacity-50 rounded-lg p-3 text-center">
                  {businesses.filter(b => b.subscriptionStatus === 'expired').length}
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="mt-8">
            <BusinessList businesses={businesses} />
          </div>
        </div>
      </main>
    </div>
  );
} 