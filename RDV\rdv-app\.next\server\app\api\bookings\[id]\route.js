/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/bookings/[id]/route";
exports.ids = ["app/api/bookings/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbookings%2F%5Bid%5D%2Froute&page=%2Fapi%2Fbookings%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbookings%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbookings%2F%5Bid%5D%2Froute&page=%2Fapi%2Fbookings%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbookings%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_wenic_RDV_rdv_app_src_app_api_bookings_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/bookings/[id]/route.ts */ \"(rsc)/./src/app/api/bookings/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/bookings/[id]/route\",\n        pathname: \"/api/bookings/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/bookings/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\RDV\\\\rdv-app\\\\src\\\\app\\\\api\\\\bookings\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_wenic_RDV_rdv_app_src_app_api_bookings_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbookings%2F%5Bid%5D%2Froute&page=%2Fapi%2Fbookings%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbookings%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/bookings/[id]/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/bookings/[id]/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_Booking__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/models/Booking */ \"(rsc)/./src/models/Booking.ts\");\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n\n\n\n\n// Configuration de l'expéditeur d'emails\nconst transporter = nodemailer__WEBPACK_IMPORTED_MODULE_3__.createTransport({\n    host: process.env.SMTP_HOST,\n    port: Number(process.env.SMTP_PORT),\n    secure: true,\n    auth: {\n        user: process.env.SMTP_USER,\n        pass: process.env.SMTP_PASSWORD\n    }\n});\nasync function GET(req, { params }) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const booking = await _models_Booking__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findById(params.id).select('-__v');\n        if (!booking) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Réservation non trouvée'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(booking);\n    } catch (error) {\n        console.error('Erreur lors de la récupération de la réservation:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Une erreur est survenue lors de la récupération de la réservation'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PATCH(req, { params }) {\n    try {\n        const { status } = await req.json();\n        if (!status || ![\n            'pending',\n            'confirmed',\n            'cancelled'\n        ].includes(status)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Statut invalide'\n            }, {\n                status: 400\n            });\n        }\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const booking = await _models_Booking__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findById(params.id);\n        if (!booking) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Réservation non trouvée'\n            }, {\n                status: 404\n            });\n        }\n        booking.status = status;\n        await booking.save();\n        // Envoyer l'email de mise à jour\n        try {\n            await transporter.sendMail({\n                from: process.env.SMTP_FROM,\n                to: booking.clientEmail,\n                subject: `Mise à jour de votre rendez-vous - ${status}`,\n                html: `\n          <h1>Mise à jour de votre rendez-vous</h1>\n          <p>Bonjour ${booking.clientName},</p>\n          <p>Votre rendez-vous du ${booking.date.toLocaleDateString('fr-FR')} à ${booking.time} a été ${status === 'confirmed' ? 'confirmé' : status === 'cancelled' ? 'annulé' : 'mis en attente'}.</p>\n          <p>Merci de votre compréhension.</p>\n        `\n            });\n        } catch (emailError) {\n            console.error('Erreur lors de l\\'envoi de l\\'email:', emailError);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Réservation mise à jour avec succès',\n            booking\n        });\n    } catch (error) {\n        console.error('Erreur lors de la mise à jour de la réservation:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Une erreur est survenue lors de la mise à jour de la réservation'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(req, { params }) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const booking = await _models_Booking__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findById(params.id);\n        if (!booking) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Réservation non trouvée'\n            }, {\n                status: 404\n            });\n        }\n        await booking.deleteOne();\n        // Envoyer l'email de confirmation de suppression\n        try {\n            await transporter.sendMail({\n                from: process.env.SMTP_FROM,\n                to: booking.clientEmail,\n                subject: 'Annulation de votre rendez-vous',\n                html: `\n          <h1>Annulation de votre rendez-vous</h1>\n          <p>Bonjour ${booking.clientName},</p>\n          <p>Votre rendez-vous du ${booking.date.toLocaleDateString('fr-FR')} à ${booking.time} a été annulé.</p>\n          <p>Nous vous remercions de votre compréhension.</p>\n        `\n            });\n        } catch (emailError) {\n            console.error('Erreur lors de l\\'envoi de l\\'email:', emailError);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Réservation supprimée avec succès'\n        });\n    } catch (error) {\n        console.error('Erreur lors de la suppression de la réservation:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Une erreur est survenue lors de la suppression de la réservation'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/bookings/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Veuillez définir l\\'URI MongoDB dans les variables d\\'environnement');\n}\nlet cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts);\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Booking.ts":
/*!*******************************!*\
  !*** ./src/models/Booking.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst bookingSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    clientId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: 'User',\n        required: [\n            true,\n            'L\\'ID du client est requis'\n        ]\n    },\n    businessId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: 'User',\n        required: [\n            true,\n            'L\\'ID du commerce est requis'\n        ]\n    },\n    service: {\n        type: String,\n        required: [\n            true,\n            'Le service est requis'\n        ]\n    },\n    date: {\n        type: Date,\n        required: [\n            true,\n            'La date est requise'\n        ]\n    },\n    time: {\n        type: String,\n        required: [\n            true,\n            'L\\'heure est requise'\n        ]\n    },\n    status: {\n        type: String,\n        enum: [\n            'pending',\n            'confirmed',\n            'cancelled',\n            'completed'\n        ],\n        default: 'pending'\n    },\n    notes: {\n        type: String,\n        default: ''\n    },\n    duration: {\n        type: Number,\n        default: 60\n    },\n    price: {\n        type: Number,\n        default: 0\n    }\n}, {\n    timestamps: true\n});\n// Index pour optimiser les recherches\nbookingSchema.index({\n    businessId: 1,\n    date: 1\n});\nbookingSchema.index({\n    clientId: 1\n});\nbookingSchema.index({\n    businessId: 1,\n    date: 1,\n    time: 1\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Booking || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('Booking', bookingSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Booking.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/nodemailer"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbookings%2F%5Bid%5D%2Froute&page=%2Fapi%2Fbookings%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbookings%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cwenic%5CRDV%5Crdv-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();