import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import AdminNav from '@/components/AdminNav';
import BusinessList from '@/components/admin/BusinessList';

async function getBusinesses() {
  await connectDB();
  const businesses = await User.find({}).select('_id name email businessName isVerified subscriptionStatus createdAt');
  return businesses.map(business => ({
    _id: business._id.toString(),
    name: business.name,
    email: business.email,
    businessName: business.businessName,
    isVerified: business.isVerified,
    subscriptionStatus: business.subscriptionStatus,
    createdAt: business.createdAt.toISOString(),
  }));
}

export default async function AdminBusinessesPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.isAdmin) {
    redirect('/');
  }

  const businesses = await getBusinesses();

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminNav />
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Gestion des Commerces</h1>
            <p className="mt-2 text-gray-600">
              Gérez les commerces inscrits sur la plateforme
            </p>
          </div>

          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Liste des Commerces</CardTitle>
              </CardHeader>
              <CardContent>
                <BusinessList businesses={businesses} />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
