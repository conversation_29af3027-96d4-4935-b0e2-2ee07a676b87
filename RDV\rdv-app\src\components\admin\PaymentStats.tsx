import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

interface PaymentStatsProps {
  stats: {
    totalAmount: number;
    totalCount: number;
    byStatus: {
      pending: { count: number; amount: number };
      completed: { count: number; amount: number };
      failed: { count: number; amount: number };
    };
    byPeriod: {
      monthly: { count: number; amount: number };
      yearly: { count: number; amount: number };
    };
    byPlan: {
      [key: string]: { count: number; amount: number };
    };
  };
}

export default function PaymentStats({ stats }: PaymentStatsProps) {
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
    }).format(amount);
  };

  // Protection contre les données manquantes
  const safeStats = {
    totalAmount: stats?.totalAmount || 0,
    totalCount: stats?.totalCount || 0,
    byStatus: {
      pending: stats?.byStatus?.pending || { count: 0, amount: 0 },
      completed: stats?.byStatus?.completed || { count: 0, amount: 0 },
      failed: stats?.byStatus?.failed || { count: 0, amount: 0 }
    },
    byPeriod: {
      monthly: stats?.byPeriod?.monthly || { count: 0, amount: 0 },
      yearly: stats?.byPeriod?.yearly || { count: 0, amount: 0 }
    },
    byPlan: stats?.byPlan || {}
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Statistiques générales */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Statistiques générales</h3>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-gray-600">Total des paiements</span>
            <span className="font-medium">{safeStats.totalCount}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Montant total</span>
            <span className="font-medium">{formatAmount(safeStats.totalAmount)}</span>
          </div>
        </div>
      </div>

      {/* Statistiques par statut */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Par statut</h3>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-gray-600">En attente</span>
            <span className="font-medium">
              {safeStats.byStatus.pending.count} ({formatAmount(safeStats.byStatus.pending.amount)})
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Complétés</span>
            <span className="font-medium">
              {safeStats.byStatus.completed.count} ({formatAmount(safeStats.byStatus.completed.amount)})
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Échoués</span>
            <span className="font-medium">
              {safeStats.byStatus.failed.count} ({formatAmount(safeStats.byStatus.failed.amount)})
            </span>
          </div>
        </div>
      </div>

      {/* Statistiques par période */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Par période</h3>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-gray-600">Mensuel</span>
            <span className="font-medium">
              {safeStats.byPeriod.monthly.count} ({formatAmount(safeStats.byPeriod.monthly.amount)})
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Annuel</span>
            <span className="font-medium">
              {safeStats.byPeriod.yearly.count} ({formatAmount(safeStats.byPeriod.yearly.amount)})
            </span>
          </div>
        </div>
      </div>

      {/* Statistiques par plan */}
      <div className="bg-white p-6 rounded-lg shadow lg:col-span-3">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Par plan d'abonnement</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(safeStats.byPlan).map(([plan, data]) => (
            <div key={plan} className="bg-gray-50 p-4 rounded">
              <h4 className="font-medium text-gray-900">{plan}</h4>
              <div className="mt-2 space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Nombre</span>
                  <span>{data.count}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Montant</span>
                  <span>{formatAmount(data.amount)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 