import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

interface PaymentStatsProps {
  stats: {
    totalAmount: number;
    totalCount: number;
    byStatus: {
      pending: { count: number; amount: number };
      completed: { count: number; amount: number };
      failed: { count: number; amount: number };
    };
    byPeriod: {
      monthly: { count: number; amount: number };
      yearly: { count: number; amount: number };
    };
    byPlan: {
      [key: string]: { count: number; amount: number };
    };
  };
}

export default function PaymentStats({ stats }: PaymentStatsProps) {
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
    }).format(amount);
  };

  // Protection contre les données manquantes
  const safeStats = {
    totalAmount: stats?.totalAmount || 0,
    totalCount: stats?.totalCount || 0,
    byStatus: {
      pending: stats?.byStatus?.pending || { count: 0, amount: 0 },
      completed: stats?.byStatus?.completed || { count: 0, amount: 0 },
      failed: stats?.byStatus?.failed || { count: 0, amount: 0 }
    },
    byPeriod: {
      monthly: stats?.byPeriod?.monthly || { count: 0, amount: 0 },
      yearly: stats?.byPeriod?.yearly || { count: 0, amount: 0 }
    },
    byPlan: stats?.byPlan || {}
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Statistiques générales */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-100 p-6 rounded-xl shadow-lg border border-blue-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-blue-900">Statistiques générales</h3>
          <div className="p-2 bg-blue-500 rounded-lg">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
        </div>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-blue-700 font-medium">Total des paiements</span>
            <span className="text-2xl font-bold text-blue-900 bg-white px-3 py-1 rounded-lg shadow-sm">
              {safeStats.totalCount}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-blue-700 font-medium">Montant total</span>
            <span className="text-2xl font-bold text-green-600 bg-white px-3 py-1 rounded-lg shadow-sm">
              {formatAmount(safeStats.totalAmount)}
            </span>
          </div>
        </div>
      </div>

      {/* Statistiques par statut */}
      <div className="bg-gradient-to-br from-green-50 to-emerald-100 p-6 rounded-xl shadow-lg border border-green-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-green-900">Par statut</h3>
          <div className="p-2 bg-green-500 rounded-lg">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-yellow-700 font-medium">En attente</span>
            <div className="text-right">
              <div className="text-lg font-bold text-yellow-600">{safeStats.byStatus.pending.count}</div>
              <div className="text-sm font-semibold text-yellow-800 bg-yellow-100 px-2 py-1 rounded">
                {formatAmount(safeStats.byStatus.pending.amount)}
              </div>
            </div>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-green-700 font-medium">Complétés</span>
            <div className="text-right">
              <div className="text-lg font-bold text-green-600">{safeStats.byStatus.completed.count}</div>
              <div className="text-sm font-semibold text-green-800 bg-green-100 px-2 py-1 rounded">
                {formatAmount(safeStats.byStatus.completed.amount)}
              </div>
            </div>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-red-700 font-medium">Échoués</span>
            <div className="text-right">
              <div className="text-lg font-bold text-red-600">{safeStats.byStatus.failed.count}</div>
              <div className="text-sm font-semibold text-red-800 bg-red-100 px-2 py-1 rounded">
                {formatAmount(safeStats.byStatus.failed.amount)}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistiques par période */}
      <div className="bg-gradient-to-br from-purple-50 to-violet-100 p-6 rounded-xl shadow-lg border border-purple-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-purple-900">Par période</h3>
          <div className="p-2 bg-purple-500 rounded-lg">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        </div>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-purple-700 font-medium">Mensuel</span>
            <div className="text-right">
              <div className="text-lg font-bold text-purple-600">{safeStats.byPeriod.monthly.count}</div>
              <div className="text-sm font-semibold text-purple-800 bg-purple-100 px-2 py-1 rounded">
                {formatAmount(safeStats.byPeriod.monthly.amount)}
              </div>
            </div>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-purple-700 font-medium">Annuel</span>
            <div className="text-right">
              <div className="text-lg font-bold text-purple-600">{safeStats.byPeriod.yearly.count}</div>
              <div className="text-sm font-semibold text-purple-800 bg-purple-100 px-2 py-1 rounded">
                {formatAmount(safeStats.byPeriod.yearly.amount)}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistiques par plan */}
      <div className="bg-gradient-to-br from-orange-50 to-amber-100 p-6 rounded-xl shadow-lg border border-orange-200 lg:col-span-3">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-orange-900">Par plan d'abonnement</h3>
          <div className="p-2 bg-orange-500 rounded-lg">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Object.entries(safeStats.byPlan).map(([plan, data], index) => {
            const colors = [
              'from-rose-100 to-pink-200 border-rose-300',
              'from-cyan-100 to-blue-200 border-cyan-300',
              'from-emerald-100 to-green-200 border-emerald-300',
              'from-violet-100 to-purple-200 border-violet-300',
              'from-amber-100 to-yellow-200 border-amber-300'
            ];
            const textColors = [
              'text-rose-800',
              'text-cyan-800',
              'text-emerald-800',
              'text-violet-800',
              'text-amber-800'
            ];
            const bgColors = [
              'bg-rose-50',
              'bg-cyan-50',
              'bg-emerald-50',
              'bg-violet-50',
              'bg-amber-50'
            ];

            return (
              <div key={plan} className={`bg-gradient-to-br ${colors[index % colors.length]} p-5 rounded-xl shadow-md border`}>
                <h4 className={`font-bold text-lg ${textColors[index % textColors.length]} mb-3 capitalize`}>
                  {plan}
                </h4>
                <div className="space-y-3">
                  <div className={`${bgColors[index % bgColors.length]} p-3 rounded-lg`}>
                    <div className="flex justify-between items-center">
                      <span className={`${textColors[index % textColors.length]} font-medium`}>Nombre</span>
                      <span className={`text-xl font-bold ${textColors[index % textColors.length]}`}>
                        {data.count}
                      </span>
                    </div>
                  </div>
                  <div className={`${bgColors[index % bgColors.length]} p-3 rounded-lg`}>
                    <div className="flex justify-between items-center">
                      <span className={`${textColors[index % textColors.length]} font-medium`}>Montant</span>
                      <span className={`text-lg font-bold ${textColors[index % textColors.length]}`}>
                        {formatAmount(data.amount)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
} 