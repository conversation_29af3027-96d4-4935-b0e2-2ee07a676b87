import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import DashboardNav from '@/components/DashboardNav';
import ScheduleForm from '@/components/ScheduleForm';

async function getUserSchedule(userId: string) {
  await connectDB();
  const user = await User.findById(userId).select('schedule');

  const defaultSchedule = {
    monday: { isOpen: true, start: '09:00', end: '18:00' },
    tuesday: { isOpen: true, start: '09:00', end: '18:00' },
    wednesday: { isOpen: true, start: '09:00', end: '18:00' },
    thursday: { isOpen: true, start: '09:00', end: '18:00' },
    friday: { isOpen: true, start: '09:00', end: '18:00' },
    saturday: { isOpen: true, start: '09:00', end: '18:00' },
    sunday: { isOpen: false, start: '09:00', end: '18:00' },
  };

  return user?.schedule || defaultSchedule;
}

export default async function SchedulePage() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    redirect('/auth/signin');
  }

  const schedule = await getUserSchedule(session.user.id);

  // Sérialiser l'objet schedule pour le passer au composant client
  const serializedSchedule = JSON.parse(JSON.stringify(schedule));

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardNav />

      <main className="py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">
              Horaires d'ouverture
            </h1>
            <p className="mt-2 text-gray-600">
              Définissez vos horaires de travail pour chaque jour de la semaine
            </p>
          </div>

          <ScheduleForm initialSchedule={serializedSchedule} />
        </div>
      </main>
    </div>
  );
}