# 🔍 Guide de Tests d'Accessibilité - Planidoo

## 📋 Checklist des fonctionnalités d'accessibilité

### ✅ Fonctionnalités implémentées

#### 🌙 Mode sombre/clair
- [x] Bouton de basculement dans la navigation
- [x] Persistance du choix utilisateur
- [x] Transition fluide entre les modes
- [x] Contraste respecté dans les deux modes
- [x] Support du mode système

#### 🌍 Internationalisation (i18n)
- [x] Support français et arabe
- [x] Sélecteur de langue dans la navigation
- [x] Direction RTL pour l'arabe
- [x] Polices adaptées (Noto Sans Arabic)
- [x] Persistance de la langue choisie

#### 🔊 Lecture vocale (Text-to-Speech)
- [x] Boutons de lecture sur les contenus importants
- [x] Contrôles pause/lecture/arrêt
- [x] Vitesse optimisée pour la clarté
- [x] Sélection automatique de voix française
- [x] Gestion des erreurs

#### ⌨️ Navigation au clavier
- [x] Focus visible amélioré
- [x] Ra<PERSON><PERSON><PERSON> clavier globaux
- [x] Lien de saut au contenu principal
- [x] Indicateur de navigation clavier
- [x] Aide contextuelle des raccourcis

#### 🎛️ Panneau d'accessibilité
- [x] Contrôle de la taille de police (75%-150%)
- [x] Mode contraste élevé
- [x] Réinitialisation des paramètres
- [x] Interface centralisée

#### 🏷️ Compatibilité lecteurs d'écran
- [x] Attributs ARIA appropriés
- [x] Balises sémantiques HTML5
- [x] Labels descriptifs
- [x] Rôles ARIA définis

## 🧪 Tests à effectuer

### 1. Tests de navigation au clavier

#### Raccourcis à tester :
- `Tab` : Navigation entre éléments
- `Shift + Tab` : Navigation inverse
- `Enter` : Activation des boutons/liens
- `Espace` : Activation des boutons
- `Échap` : Fermeture des modales/menus
- `Ctrl + H` : Aide des raccourcis
- `Ctrl + /` : Focus sur recherche
- `Ctrl + K` : Menu navigation

#### Vérifications :
- [ ] Tous les éléments interactifs sont accessibles
- [ ] L'ordre de tabulation est logique
- [ ] Le focus est toujours visible
- [ ] Aucun piège de focus (focus trap)

### 2. Tests de lecteur d'écran

#### Outils recommandés :
- **Windows** : NVDA (gratuit), JAWS
- **macOS** : VoiceOver (intégré)
- **Linux** : Orca

#### Points à vérifier :
- [ ] Tous les textes sont lus correctement
- [ ] Les images ont des alt-text appropriés
- [ ] Les formulaires sont bien étiquetés
- [ ] La structure de page est claire
- [ ] Les changements d'état sont annoncés

### 3. Tests de contraste

#### Outils :
- WebAIM Contrast Checker
- Colour Contrast Analyser
- DevTools Accessibility

#### Critères WCAG 2.1 :
- [ ] Contraste normal : 4.5:1 minimum
- [ ] Contraste large : 3:1 minimum
- [ ] Mode contraste élevé : 7:1 minimum

### 4. Tests de Text-to-Speech

#### Navigateurs supportés :
- [ ] Chrome/Edge : Excellent support
- [ ] Firefox : Support partiel
- [ ] Safari : Support natif

#### Vérifications :
- [ ] Lecture claire et compréhensible
- [ ] Contrôles fonctionnels
- [ ] Pas de lecture des éléments cachés
- [ ] Gestion des langues multiples

### 5. Tests d'internationalisation

#### Tests RTL (arabe) :
- [ ] Direction du texte correcte
- [ ] Alignement des éléments inversé
- [ ] Icônes et boutons repositionnés
- [ ] Formulaires fonctionnels

#### Tests de langue :
- [ ] Changement de langue immédiat
- [ ] Persistance entre sessions
- [ ] Polices appropriées chargées
- [ ] Dates et nombres formatés

## 🛠️ Outils de test recommandés

### Extensions navigateur :
1. **axe DevTools** - Tests automatisés
2. **WAVE** - Évaluation visuelle
3. **Lighthouse** - Audit complet
4. **Color Oracle** - Simulation daltonisme

### Outils en ligne :
1. **WebAIM** - Vérificateur de contraste
2. **Accessible Colors** - Générateur de couleurs
3. **Hemingway Editor** - Lisibilité du texte

### Tests manuels :
1. **Navigation sans souris** - 15 minutes
2. **Zoom à 200%** - Vérifier la lisibilité
3. **Mode sombre/clair** - Transitions
4. **Différentes tailles d'écran** - Responsive

## 📊 Métriques d'accessibilité

### Objectifs WCAG 2.1 AA :
- [ ] **Perceptible** : Informations présentables
- [ ] **Utilisable** : Interface utilisable
- [ ] **Compréhensible** : Informations compréhensibles
- [ ] **Robuste** : Contenu robuste

### Score Lighthouse cible :
- **Accessibilité** : 95+ / 100
- **Performance** : 90+ / 100
- **SEO** : 95+ / 100
- **Bonnes pratiques** : 90+ / 100

## 🚀 Tests de performance

### Avec fonctionnalités d'accessibilité :
- [ ] Temps de chargement < 3s
- [ ] First Contentful Paint < 1.5s
- [ ] Largest Contentful Paint < 2.5s
- [ ] Cumulative Layout Shift < 0.1

## 📝 Rapport de test

### Template de rapport :
```
Date : ___________
Testeur : ___________
Navigateur : ___________
Dispositif : ___________

Fonctionnalités testées :
□ Mode sombre/clair
□ Sélection de langue
□ Text-to-Speech
□ Navigation clavier
□ Lecteur d'écran

Problèmes identifiés :
1. ___________
2. ___________

Recommandations :
1. ___________
2. ___________

Score global : ___/100
```

## 🔄 Tests de régression

### À chaque mise à jour :
- [ ] Vérifier tous les raccourcis clavier
- [ ] Tester le changement de thème
- [ ] Valider la lecture vocale
- [ ] Contrôler la navigation RTL
- [ ] Vérifier les contrastes

### Tests automatisés recommandés :
```bash
# Tests Lighthouse
npm run lighthouse

# Tests axe-core
npm run test:a11y

# Tests de contraste
npm run test:contrast
```

## 📞 Support et ressources

### Documentation :
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [MDN Accessibility](https://developer.mozilla.org/en-US/docs/Web/Accessibility)
- [WebAIM Resources](https://webaim.org/resources/)

### Communauté :
- [A11y Slack](https://web-a11y.slack.com/)
- [Accessibility Reddit](https://www.reddit.com/r/accessibility/)

---

**Note** : Ce guide doit être mis à jour régulièrement selon les évolutions des standards d'accessibilité et les retours utilisateurs.
