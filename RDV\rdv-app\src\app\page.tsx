import Link from 'next/link';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from '@/components/ui/Card';
import Button from '@/components/ui/Button';

export default function Home() {
  return (
    <div className="space-y-16">
      <div className="text-center relative">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 rounded-3xl opacity-50 blur-3xl"></div>
        <div className="relative z-10 py-16">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl">
            <span className="block">Simplifiez la gestion de vos</span>
            <span className="block bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              rendez-vous en ligne
            </span>
          </h1>
          <p className="mx-auto mt-6 max-w-md text-base text-gray-600 sm:text-lg md:mt-8 md:max-w-3xl md:text-xl">
            <span className="font-semibold text-purple-600">Planidoo</span> - La solution moderne pour gérer vos rendez-vous et développer votre activité.
            Inscrivez-vous gratuitement et commencez à accepter des réservations en ligne.
          </p>
          <div className="mx-auto mt-8 max-w-md sm:flex sm:justify-center md:mt-12 space-y-3 sm:space-y-0 sm:space-x-4">
            <Link href="/auth/signup">
              <Button variant="gradient" size="lg" className="w-full sm:w-auto flex items-center justify-center space-x-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <span>Commencer gratuitement</span>
              </Button>
            </Link>
            <Link href="/auth/signin">
              <Button variant="outline" size="lg" className="w-full sm:w-auto flex items-center justify-center space-x-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                <span>Se connecter</span>
              </Button>
            </Link>
          </div>
        </div>
      </div>

      <div className="mt-20">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Pourquoi choisir <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Planidoo</span> ?
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Découvrez les fonctionnalités qui font de Planidoo la solution idéale pour votre activité
          </p>
        </div>
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          <Card className="bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-500 rounded-lg">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <CardTitle className="text-blue-900">Gestion des horaires</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="text-blue-800">
              Définissez facilement vos horaires d'ouverture et de fermeture pour chaque jour de la semaine avec notre interface intuitive.
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-emerald-100 border-green-200">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-500 rounded-lg">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <CardTitle className="text-green-900">Réservations en ligne</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="text-green-800">
              Permettez à vos clients de réserver des rendez-vous 24h/24 et 7j/7 directement en ligne via votre page personnalisée.
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-violet-100 border-purple-200">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-500 rounded-lg">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z" />
                  </svg>
                </div>
                <CardTitle className="text-purple-900">Notifications automatiques</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="text-purple-800">
              Recevez des notifications par email pour chaque nouvelle réservation et envoyez des confirmations automatiques à vos clients.
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
