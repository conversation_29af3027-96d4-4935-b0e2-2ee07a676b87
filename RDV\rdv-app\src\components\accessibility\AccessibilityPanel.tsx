'use client';

import { useState } from 'react';
import ThemeToggle from './ThemeToggle';
import LanguageToggle from './LanguageToggle';
import TextToSpeech from './TextToSpeech';
import Button from '@/components/ui/Button';

export default function AccessibilityPanel() {
  const [isOpen, setIsOpen] = useState(false);
  const [fontSize, setFontSize] = useState(100);
  const [highContrast, setHighContrast] = useState(false);

  const adjustFontSize = (increment: number) => {
    const newSize = Math.max(75, Math.min(150, fontSize + increment));
    setFontSize(newSize);
    document.documentElement.style.fontSize = `${newSize}%`;
  };

  const toggleHighContrast = () => {
    setHighContrast(!highContrast);
    if (!highContrast) {
      document.documentElement.classList.add('high-contrast');
    } else {
      document.documentElement.classList.remove('high-contrast');
    }
  };

  const resetAccessibility = () => {
    setFontSize(100);
    setHighContrast(false);
    document.documentElement.style.fontSize = '100%';
    document.documentElement.classList.remove('high-contrast');
  };

  return (
    <>
      {/* Bouton d'ouverture du panneau */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-4 right-4 bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-full shadow-lg z-40 transition-all duration-200 hover:scale-110"
        aria-label="Ouvrir le panneau d'accessibilité"
        aria-expanded={isOpen}
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
        </svg>
      </button>

      {/* Panneau d'accessibilité */}
      {isOpen && (
        <>
          {/* Overlay */}
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setIsOpen(false)}
            aria-hidden="true"
          />
          
          {/* Panneau */}
          <div 
            className="fixed bottom-20 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-80 max-h-96 overflow-y-auto"
            role="dialog"
            aria-modal="true"
            aria-labelledby="accessibility-panel-title"
          >
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h2 id="accessibility-panel-title" className="text-lg font-semibold text-gray-900 dark:text-white">
                  Accessibilité
                </h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  aria-label="Fermer le panneau d'accessibilité"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="p-4 space-y-6">
              {/* Thème */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Apparence
                </h3>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Mode sombre/clair</span>
                  <ThemeToggle />
                </div>
              </div>

              {/* Langue */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Langue
                </h3>
                <LanguageToggle />
              </div>

              {/* Taille de police */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Taille du texte
                </h3>
                <div className="flex items-center justify-between">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => adjustFontSize(-10)}
                    aria-label="Diminuer la taille du texte"
                    disabled={fontSize <= 75}
                  >
                    A-
                  </Button>
                  <span className="text-sm text-gray-600 dark:text-gray-400 mx-3">
                    {fontSize}%
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => adjustFontSize(10)}
                    aria-label="Augmenter la taille du texte"
                    disabled={fontSize >= 150}
                  >
                    A+
                  </Button>
                </div>
              </div>

              {/* Contraste élevé */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Contraste
                </h3>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Contraste élevé</span>
                  <Button
                    variant={highContrast ? "primary" : "outline"}
                    size="sm"
                    onClick={toggleHighContrast}
                    aria-label={`${highContrast ? 'Désactiver' : 'Activer'} le contraste élevé`}
                  >
                    {highContrast ? 'ON' : 'OFF'}
                  </Button>
                </div>
              </div>

              {/* Lecture vocale */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Lecture vocale
                </h3>
                <TextToSpeech 
                  text="Bienvenue dans le panneau d'accessibilité de Planidoo. Vous pouvez ajuster les paramètres pour améliorer votre expérience."
                  showLabel={true}
                  className="w-full"
                />
              </div>

              {/* Réinitialiser */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <Button
                  variant="outline"
                  onClick={resetAccessibility}
                  className="w-full"
                  aria-label="Réinitialiser tous les paramètres d'accessibilité"
                >
                  Réinitialiser
                </Button>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Styles CSS pour le contraste élevé */}
      <style jsx global>{`
        .high-contrast {
          filter: contrast(150%) brightness(110%);
        }
        
        .high-contrast * {
          text-shadow: none !important;
          box-shadow: none !important;
        }
        
        .high-contrast button,
        .high-contrast a,
        .high-contrast input,
        .high-contrast textarea {
          border: 2px solid #000 !important;
        }
        
        .high-contrast .bg-gradient-to-r,
        .high-contrast .bg-gradient-to-br,
        .high-contrast .bg-gradient-to-l,
        .high-contrast .bg-gradient-to-t {
          background: #fff !important;
          color: #000 !important;
        }
      `}</style>
    </>
  );
}
