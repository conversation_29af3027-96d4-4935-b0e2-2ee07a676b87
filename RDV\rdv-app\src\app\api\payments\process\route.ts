import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import Payment from '@/models/Payment';

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: 'Non autorisé' },
        { status: 401 }
      );
    }

    const { plan, period, amount, paymentMethod, cardData } = await request.json();

    // Validation des données
    if (!plan || !period || !amount || !paymentMethod) {
      return NextResponse.json(
        { success: false, message: 'Données manquantes' },
        { status: 400 }
      );
    }

    await connectDB();

    // Récupérer l'utilisateur
    const user = await User.findById(session.user.id);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Utilisateur non trouvé' },
        { status: 404 }
      );
    }

    // Simulation du traitement de paiement
    // Dans un vrai projet, ici vous intégreriez Stripe, PayPal, etc.
    const paymentSuccess = await simulatePayment(cardData, amount);

    if (!paymentSuccess) {
      return NextResponse.json(
        { success: false, message: 'Paiement refusé' },
        { status: 400 }
      );
    }

    // Calculer les dates d'abonnement
    const now = new Date();
    const subscriptionStartDate = now;
    const subscriptionEndDate = new Date(now);
    
    if (period === 'monthly') {
      subscriptionEndDate.setMonth(subscriptionEndDate.getMonth() + 1);
    } else {
      subscriptionEndDate.setFullYear(subscriptionEndDate.getFullYear() + 1);
    }

    // Définir les limites selon le plan
    let maxBookingsPerMonth = 10;
    let maxServices = 3;

    if (plan === 'premium') {
      maxBookingsPerMonth = -1; // Illimité
      maxServices = -1; // Illimité
    } else if (plan === 'enterprise') {
      maxBookingsPerMonth = -1; // Illimité
      maxServices = -1; // Illimité
    }

    // Mettre à jour l'utilisateur
    await User.findByIdAndUpdate(session.user.id, {
      subscriptionStatus: 'active',
      subscriptionPlan: plan,
      subscriptionStartDate,
      subscriptionEndDate,
      maxBookingsPerMonth,
      maxServices,
    });

    // Créer l'enregistrement de paiement
    const payment = new Payment({
      userId: session.user.id,
      amount,
      currency: 'EUR',
      status: 'completed',
      paymentMethod: 'card',
      subscriptionPlan: plan,
      subscriptionPeriod: period,
      startDate: subscriptionStartDate,
      endDate: subscriptionEndDate,
      transactionId: generateTransactionId(),
    });

    await payment.save();

    return NextResponse.json({
      success: true,
      message: 'Paiement traité avec succès',
      payment: {
        id: payment._id,
        amount,
        plan,
        period,
        startDate: subscriptionStartDate.toISOString(),
        endDate: subscriptionEndDate.toISOString(),
      }
    });

  } catch (error: any) {
    console.error('Erreur lors du traitement du paiement:', error);
    return NextResponse.json(
      { success: false, message: 'Erreur serveur', error: error.message },
      { status: 500 }
    );
  }
}

// Simulation de traitement de paiement
async function simulatePayment(cardData: any, amount: number): Promise<boolean> {
  // Simulation d'un délai de traitement
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Simulation de validation de carte
  if (!cardData.cardNumber || !cardData.expiryDate || !cardData.cvv || !cardData.cardName) {
    return false;
  }

  // Simulation d'échec pour certains numéros de carte (pour les tests)
  if (cardData.cardNumber.includes('0000')) {
    return false;
  }

  // Simulation de succès pour les autres cas
  return true;
}

// Génération d'un ID de transaction unique
function generateTransactionId(): string {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 8);
  return `txn_${timestamp}_${random}`;
}
