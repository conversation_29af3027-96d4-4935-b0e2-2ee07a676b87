import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import Payment from '@/models/Payment';
import DashboardNav from '@/components/DashboardNav';
import BillingManager from '@/components/BillingManager';

async function getUserBillingData(userId: string) {
  await connectDB();
  
  const user = await User.findById(userId).select(
    'subscriptionStatus subscriptionPlan trialStartDate trialEndDate subscriptionStartDate subscriptionEndDate maxBookingsPerMonth maxServices'
  );
  
  if (!user) {
    return null;
  }

  // Récupérer l'historique des paiements
  const payments = await Payment.find({ userId }).sort({ createdAt: -1 }).limit(10);

  return {
    user: {
      _id: user._id.toString(),
      subscriptionStatus: user.subscriptionStatus,
      subscriptionPlan: user.subscriptionPlan,
      trialStartDate: user.trialStartDate?.toISOString(),
      trialEndDate: user.trialEndDate?.toISOString(),
      subscriptionStartDate: user.subscriptionStartDate?.toISOString(),
      subscriptionEndDate: user.subscriptionEndDate?.toISOString(),
      maxBookingsPerMonth: user.maxBookingsPerMonth,
      maxServices: user.maxServices,
    },
    payments: payments.map(payment => ({
      _id: payment._id.toString(),
      amount: payment.amount,
      currency: payment.currency,
      status: payment.status,
      subscriptionPlan: payment.subscriptionPlan,
      subscriptionPeriod: payment.subscriptionPeriod,
      createdAt: payment.createdAt.toISOString(),
    }))
  };
}

export default async function BillingPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    redirect('/auth/signin');
  }

  const billingData = await getUserBillingData(session.user.id);

  if (!billingData) {
    redirect('/auth/signin');
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardNav />
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Facturation et abonnement</h1>
            <p className="mt-2 text-gray-600">
              Gérez votre abonnement et consultez votre historique de paiements
            </p>
          </div>

          <BillingManager billingData={billingData} />
        </div>
      </div>
    </div>
  );
}
