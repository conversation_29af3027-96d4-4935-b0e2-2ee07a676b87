import { notFound } from 'next/navigation';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import BusinessVitrine from '@/components/BusinessVitrine';

async function getBusinessData(businessId: string) {
  try {
    await connectDB();
    const business = await User.findById(businessId).select(
      'businessName businessDescription businessImages socialMedia coordinates businessCategory businessTags isVitrineActive phone address services schedule'
    );

    if (!business || !business.isVitrineActive) {
      return null;
    }

    // Sérialiser complètement les données pour éviter les erreurs de passage d'objets MongoDB
    const businessData = JSON.parse(JSON.stringify(business.toObject()));

    return {
      _id: businessData._id.toString(),
      businessName: businessData.businessName || '',
      businessDescription: businessData.businessDescription || '',
      businessImages: Array.isArray(businessData.businessImages) ? businessData.businessImages : [],
      socialMedia: {
        facebook: businessData.socialMedia?.facebook || '',
        instagram: businessData.socialMedia?.instagram || '',
        twitter: businessData.socialMedia?.twitter || '',
        linkedin: businessData.socialMedia?.linkedin || '',
        website: businessData.socialMedia?.website || '',
      },
      coordinates: {
        latitude: businessData.coordinates?.latitude || 0,
        longitude: businessData.coordinates?.longitude || 0,
      },
      businessCategory: businessData.businessCategory || '',
      businessTags: Array.isArray(businessData.businessTags) ? businessData.businessTags : [],
      phone: businessData.phone || '',
      address: businessData.address || '',
      services: Array.isArray(businessData.services) ? businessData.services : [],
      schedule: businessData.schedule || {},
    };
  } catch (error) {
    console.error('Erreur lors de la récupération des données du commerce:', error);
    return null;
  }
}

interface PageProps {
  params: Promise<{ businessId: string }>;
}

export default async function VitrinePage({ params }: PageProps) {
  const { businessId } = await params;
  const businessData = await getBusinessData(businessId);

  if (!businessData) {
    notFound();
  }

  return <BusinessVitrine business={businessData} />;
}

export async function generateMetadata({ params }: PageProps) {
  const { businessId } = await params;
  const businessData = await getBusinessData(businessId);

  if (!businessData) {
    return {
      title: 'Commerce non trouvé',
    };
  }

  return {
    title: `${businessData.businessName} - Site vitrine`,
    description: businessData.businessDescription || `Découvrez ${businessData.businessName} et ses services`,
    openGraph: {
      title: businessData.businessName,
      description: businessData.businessDescription || `Découvrez ${businessData.businessName} et ses services`,
      images: businessData.businessImages.length > 0 ? [businessData.businessImages[0]] : [],
    },
  };
}
