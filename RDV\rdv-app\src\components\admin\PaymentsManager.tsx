'use client';

import { useState, useEffect } from 'react';
import PaymentFilters from './PaymentFilters';
import PaymentList from './PaymentList';
import PaymentStats from './PaymentStats';

interface Payment {
  _id: string;
  userId: {
    name: string;
    email: string;
    businessName: string;
  };
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed';
  paymentMethod: string;
  subscriptionPlan: string;
  subscriptionPeriod: 'monthly' | 'yearly';
  startDate: string;
  endDate: string;
  createdAt: string;
}

interface PaymentsManagerProps {
  initialPayments: Payment[];
  initialStats: any;
}

export default function PaymentsManager({ initialPayments, initialStats }: PaymentsManagerProps) {
  const [payments, setPayments] = useState<Payment[]>(initialPayments);
  const [stats, setStats] = useState(initialStats);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleFilterChange = async (filters: {
    status?: string;
    startDate?: string;
    endDate?: string;
    minAmount?: number;
    maxAmount?: number;
  }) => {
    try {
      setLoading(true);
      setError('');

      // Construire les paramètres de requête
      const params = new URLSearchParams();
      
      if (filters.status) params.append('status', filters.status);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.minAmount !== undefined) params.append('minAmount', filters.minAmount.toString());
      if (filters.maxAmount !== undefined) params.append('maxAmount', filters.maxAmount.toString());

      const response = await fetch(`/api/admin/payments?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Erreur lors du chargement des paiements');
      }

      const data = await response.json();
      setPayments(data.payments);

      // Recalculer les statistiques pour les données filtrées
      const filteredStats = calculateStats(data.payments);
      setStats(filteredStats);

    } catch (err: any) {
      console.error('Erreur lors du filtrage:', err);
      setError(err.message || 'Erreur lors du filtrage des paiements');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (paymentsData: Payment[]) => {
    const totalAmount = paymentsData.reduce((sum, payment) => sum + payment.amount, 0);
    const totalCount = paymentsData.length;

    const byStatus = {
      pending: { count: 0, amount: 0 },
      completed: { count: 0, amount: 0 },
      failed: { count: 0, amount: 0 }
    };

    const byPeriod = {
      monthly: { count: 0, amount: 0 },
      yearly: { count: 0, amount: 0 }
    };

    const byPlan: any = {};

    paymentsData.forEach(payment => {
      // Statistiques par statut
      if (byStatus[payment.status]) {
        byStatus[payment.status].count++;
        byStatus[payment.status].amount += payment.amount;
      }

      // Statistiques par période
      if (byPeriod[payment.subscriptionPeriod]) {
        byPeriod[payment.subscriptionPeriod].count++;
        byPeriod[payment.subscriptionPeriod].amount += payment.amount;
      }

      // Statistiques par plan
      if (!byPlan[payment.subscriptionPlan]) {
        byPlan[payment.subscriptionPlan] = { count: 0, amount: 0 };
      }
      byPlan[payment.subscriptionPlan].count++;
      byPlan[payment.subscriptionPlan].amount += payment.amount;
    });

    return {
      totalAmount,
      totalCount,
      byStatus,
      byPeriod,
      byPlan
    };
  };

  return (
    <div className="space-y-8">
      {/* Statistiques */}
      <PaymentStats stats={stats} />

      {/* Filtres */}
      <div className="relative">
        <PaymentFilters onFilterChange={handleFilterChange} />
        {loading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm text-gray-600">Filtrage en cours...</span>
            </div>
          </div>
        )}
      </div>

      {/* Messages d'erreur */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Résultats */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Paiements ({payments.length} résultat{payments.length !== 1 ? 's' : ''})
          </h3>
        </div>
        <PaymentList payments={payments} />
      </div>
    </div>
  );
}
