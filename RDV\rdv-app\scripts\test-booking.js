// Script de test pour vérifier les réservations
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testBookingFlow() {
  console.log('🧪 Test du flux de réservation...\n');

  // Test 1: Récupérer les infos d'un commerce
  console.log('1. Test de récupération des infos commerce...');
  try {
    const businessResponse = await fetch(`${BASE_URL}/api/business/68438f8b49332729c595d304`);
    const businessData = await businessResponse.json();
    
    if (businessResponse.ok) {
      console.log('✅ Commerce trouvé:', businessData.businessName);
      console.log('   Services:', businessData.services);
      console.log('   Horaires:', Object.keys(businessData.workingHours));
    } else {
      console.log('❌ Erreur:', businessData.message);
      return;
    }
  } catch (error) {
    console.log('❌ Erreur de connexion:', error.message);
    return;
  }

  // Test 2: Créer une réservation
  console.log('\n2. Test de création de réservation...');
  try {
    const bookingData = {
      userId: '68438f8b49332729c595d304',
      clientName: 'Test Client',
      clientEmail: '<EMAIL>',
      service: 'Coupe femme',
      notes: 'Test de réservation automatique',
      date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Demain
      time: '14:00'
    };

    const bookingResponse = await fetch(`${BASE_URL}/api/bookings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(bookingData),
    });

    const bookingResult = await bookingResponse.json();
    
    if (bookingResponse.ok) {
      console.log('✅ Réservation créée avec succès!');
      console.log('   ID:', bookingResult.booking._id);
      console.log('   Service:', bookingResult.booking.service);
      console.log('   Date:', bookingResult.booking.date);
      console.log('   Heure:', bookingResult.booking.time);
    } else {
      console.log('❌ Erreur lors de la réservation:', bookingResult.message);
    }
  } catch (error) {
    console.log('❌ Erreur de connexion:', error.message);
  }

  console.log('\n🎉 Test terminé!');
}

// Exécuter le test
testBookingFlow();
