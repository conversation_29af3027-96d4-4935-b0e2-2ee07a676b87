import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { message: 'Non autorisé' },
        { status: 401 }
      );
    }

    const schedule = await req.json();

    await connectDB();

    const user = await User.findByIdAndUpdate(
      session.user.id,
      { schedule: schedule },
      { new: true }
    );

    if (!user) {
      return NextResponse.json(
        { message: 'Utilisateur non trouvé' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { message: 'Horaires mis à jour avec succès' },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('Erreur lors de la mise à jour des horaires:', error);
    return NextResponse.json(
      { message: 'Une erreur est survenue lors de la mise à jour des horaires' },
      { status: 500 }
    );
  }
} 