import Link from 'next/link';
import Image from 'next/image';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

async function getActiveVitrines() {
  try {
    await connectDB();
    const businesses = await User.find({ 
      isVitrineActive: true,
      isVerified: true 
    }).select(
      'businessName businessDescription businessCategory businessTags businessImages address'
    ).limit(20);
    
    return businesses.map(business => ({
      _id: business._id.toString(),
      businessName: business.businessName,
      businessDescription: business.businessDescription || '',
      businessCategory: business.businessCategory || '',
      businessTags: business.businessTags || [],
      businessImages: business.businessImages || [],
      address: business.address || '',
    }));
  } catch (error) {
    console.error('Erreur lors de la récupération des vitrines:', error);
    return [];
  }
}

export default async function VitrinesPage() {
  const vitrines = await getActiveVitrines();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900">Découvrez nos commerces</h1>
            <p className="mt-2 text-gray-600">
              Trouvez le commerce parfait pour vos besoins et prenez rendez-vous en ligne
            </p>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {vitrines.length === 0 ? (
          <div className="text-center py-12">
            <h2 className="text-xl font-medium text-gray-900 mb-2">
              Aucun commerce disponible pour le moment
            </h2>
            <p className="text-gray-600">
              Les commerces apparaîtront ici une fois qu'ils auront activé leur site vitrine.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {vitrines.map((vitrine) => (
              <Card key={vitrine._id} className="hover:shadow-lg transition-shadow">
                <div className="relative h-48">
                  {vitrine.businessImages.length > 0 ? (
                    <Image
                      src={vitrine.businessImages[0]}
                      alt={vitrine.businessName}
                      fill
                      className="object-cover rounded-t-lg"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200 rounded-t-lg flex items-center justify-center">
                      <span className="text-gray-400 text-4xl">🏪</span>
                    </div>
                  )}
                </div>
                
                <CardHeader>
                  <CardTitle className="text-lg">{vitrine.businessName}</CardTitle>
                  {vitrine.businessCategory && (
                    <p className="text-sm text-gray-600">{vitrine.businessCategory}</p>
                  )}
                </CardHeader>
                
                <CardContent>
                  {vitrine.businessDescription && (
                    <p className="text-gray-700 text-sm mb-4 line-clamp-3">
                      {vitrine.businessDescription.length > 120
                        ? `${vitrine.businessDescription.substring(0, 120)}...`
                        : vitrine.businessDescription}
                    </p>
                  )}
                  
                  {vitrine.address && (
                    <p className="text-gray-600 text-sm mb-4 flex items-center">
                      <span className="mr-2">📍</span>
                      {vitrine.address}
                    </p>
                  )}
                  
                  {vitrine.businessTags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mb-4">
                      {vitrine.businessTags.slice(0, 3).map((tag, index) => (
                        <span
                          key={index}
                          className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs"
                        >
                          {tag}
                        </span>
                      ))}
                      {vitrine.businessTags.length > 3 && (
                        <span className="text-gray-500 text-xs">
                          +{vitrine.businessTags.length - 3} autres
                        </span>
                      )}
                    </div>
                  )}
                  
                  <div className="flex space-x-2">
                    <Link
                      href={`/vitrine/${vitrine._id}`}
                      className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-center hover:bg-gray-200 transition-colors text-sm"
                    >
                      Voir la vitrine
                    </Link>
                    <Link
                      href={`/book/${vitrine._id}`}
                      className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg text-center hover:bg-blue-700 transition-colors text-sm"
                    >
                      Réserver
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </main>
    </div>
  );
}

export const metadata = {
  title: 'Découvrez nos commerces - RDV Platform',
  description: 'Trouvez le commerce parfait pour vos besoins et prenez rendez-vous en ligne',
};
