import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import connectDB from '@/lib/mongodb';
import Booking from '@/models/Booking';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import DashboardNav from '@/components/DashboardNav';
import BookingList from '@/components/BookingList';

async function getBookings(userId: string) {
  await connectDB();
  const bookings = await Booking.find({ businessId: userId })
    .populate('clientId', 'name email')
    .sort({ date: -1 });
  
  return bookings.map(booking => ({
    _id: booking._id.toString(),
    clientId: {
      name: booking.clientId?.name || 'Client supprimé',
      email: booking.clientId?.email || '',
    },
    service: booking.service,
    date: booking.date.toISOString(),
    time: booking.time,
    status: booking.status,
    notes: booking.notes,
    createdAt: booking.createdAt.toISOString(),
  }));
}

export default async function DashboardBookingsPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    redirect('/auth/signin');
  }

  const bookings = await getBookings(session.user.id);

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardNav />
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Mes Réservations</h1>
            <p className="mt-2 text-gray-600">
              Gérez toutes vos réservations clients
            </p>
          </div>

          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Liste des Réservations</CardTitle>
              </CardHeader>
              <CardContent>
                <BookingList bookings={bookings} />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
