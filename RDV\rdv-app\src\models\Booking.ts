import mongoose from 'mongoose';

export interface IBooking extends mongoose.Document {
  clientId: mongoose.Types.ObjectId;
  businessId: mongoose.Types.ObjectId;
  service: string;
  date: Date;
  time: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  notes: string;
  duration: number;
  price: number;
  createdAt: Date;
  updatedAt: Date;
}

const bookingSchema = new mongoose.Schema({
  clientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'L\'ID du client est requis'],
  },
  businessId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'L\'ID du commerce est requis'],
  },
  service: {
    type: String,
    required: [true, 'Le service est requis'],
  },
  date: {
    type: Date,
    required: [true, 'La date est requise'],
  },
  time: {
    type: String,
    required: [true, 'L\'heure est requise'],
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'cancelled', 'completed'],
    default: 'pending',
  },
  notes: {
    type: String,
    default: '',
  },
  duration: {
    type: Number,
    default: 60, // en minutes
  },
  price: {
    type: Number,
    default: 0,
  },
}, {
  timestamps: true,
});

// Index pour optimiser les recherches
bookingSchema.index({ businessId: 1, date: 1 });
bookingSchema.index({ clientId: 1 });
bookingSchema.index({ businessId: 1, date: 1, time: 1 });

export default mongoose.models.Booking || mongoose.model<IBooking>('Booking', bookingSchema); 