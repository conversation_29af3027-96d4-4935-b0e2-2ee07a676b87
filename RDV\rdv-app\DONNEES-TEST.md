# 🧪 Données de Test - RDV Platform

Ce guide vous explique comment ajouter des données de test pour tester toutes les fonctionnalités de la plateforme.

## 🚀 Installation rapide des données de test

### Méthode 1 : Script simple (recommandé)
```bash
node scripts/seed-simple.js
```

### Méthode 2 : Via npm
```bash
npm run seed:dev
```

## 📋 Comptes créés automatiquement

### 👑 Administrateur
- **Email :** `<EMAIL>`
- **Mot de passe :** `admin123`
- **Accès :** Panel d'administration complet

### 👤 Client de test
- **Email :** `<EMAIL>`
- **Mot de passe :** `password123`
- **Utilisation :** Pour tester les réservations

### 🏪 Commerces avec vitrines actives

#### 💇‍♀️ Salon de Coiffure
- **Email :** `<EMAIL>`
- **Mot de passe :** `password123`
- **Commerce :** Salon Sophie
- **Services :** Coupe femme, Coupe homme, Coloration, Brushing
- **Vitrine :** ✅ Active

#### 🍽️ Restaurant
- **Email :** `<EMAIL>`
- **Mot de passe :** `password123`
- **Commerce :** Restaurant Les Saveurs
- **Services :** Déjeuner, Dîner, Brunch, Événements privés
- **Vitrine :** ✅ Active

#### 💅 Institut de Beauté
- **Email :** `<EMAIL>`
- **Mot de passe :** `password123`
- **Commerce :** Beauté & Wellness
- **Services :** Soin du visage, Massage, Manucure, Pédicure
- **Vitrine :** ✅ Active

## 🔗 URLs de test

### Pages principales
- **Accueil :** http://localhost:3000
- **Découvrir les commerces :** http://localhost:3000/vitrines
- **Liste des commerces :** http://localhost:3000/businesses

### Administration
- **Panel admin :** http://localhost:3000/admin
- **Gestion des commerces :** http://localhost:3000/admin/businesses
- **Gestion des paiements :** http://localhost:3000/admin/payments

### Dashboard commerces
- **Tableau de bord :** http://localhost:3000/dashboard
- **Réservations :** http://localhost:3000/dashboard/bookings
- **Horaires :** http://localhost:3000/dashboard/schedule
- **Site vitrine :** http://localhost:3000/dashboard/vitrine
- **Paramètres :** http://localhost:3000/dashboard/settings

### Vitrines publiques
Les URLs exactes des vitrines seront affichées après l'exécution du script.

## 📊 Données créées

### ✅ Réservations
- 3 réservations de test avec différents statuts
- Répartition sur plusieurs jours
- Prix et durées variables

### 💳 Paiements
- Abonnements mensuels et annuels
- Différents statuts (complété, en attente, échoué)
- Montants réalistes

### 🏪 Vitrines
- Descriptions complètes des commerces
- Coordonnées GPS (Paris)
- Réseaux sociaux
- Horaires d'ouverture
- Catégories et tags

## 🧹 Réinitialiser les données

Le script supprime automatiquement toutes les données existantes avant de créer les nouvelles. Pour une réinitialisation manuelle :

```bash
# Supprimer toutes les données
node -e "
const mongoose = require('mongoose');
mongoose.connect('mongodb://localhost:27017/rdv-app').then(() => {
  return mongoose.connection.db.dropDatabase();
}).then(() => {
  console.log('Base de données supprimée');
  process.exit(0);
});
"
```

## 🔧 Personnalisation

Pour modifier les données de test, éditez le fichier `scripts/seed-simple.js` :

- Ajoutez de nouveaux commerces
- Modifiez les services proposés
- Changez les coordonnées GPS
- Ajustez les horaires d'ouverture

## 🐛 Résolution de problèmes

### Erreur de connexion MongoDB
```bash
# Vérifiez que MongoDB est démarré
mongod --version
```

### Erreur "Email already exists"
```bash
# Supprimez la base de données et relancez
node scripts/seed-simple.js
```

### Problème de permissions
```bash
# Exécutez avec les droits administrateur si nécessaire
```

## 🎯 Scénarios de test recommandés

1. **Test admin :**
   - Connectez-vous en tant qu'admin
   - Vérifiez la liste des commerces
   - Consultez les paiements

2. **Test commerce :**
   - Connectez-vous en tant que commerce
   - Gérez vos réservations
   - Modifiez votre vitrine
   - Configurez vos horaires

3. **Test client :**
   - Parcourez les vitrines
   - Prenez un rendez-vous
   - Consultez vos réservations

4. **Test vitrine :**
   - Visitez les vitrines publiques
   - Testez la géolocalisation
   - Vérifiez les réseaux sociaux

## 📞 Support

Si vous rencontrez des problèmes avec les données de test, vérifiez :
- MongoDB est démarré
- Les dépendances sont installées (`npm install`)
- Le serveur Next.js fonctionne (`npm run dev`)

Bon test ! 🚀
