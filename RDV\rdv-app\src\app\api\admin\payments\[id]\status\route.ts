import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import Payment from '@/models/Payment';
import User from '@/models/User';

export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { status } = await request.json();
    if (!['completed', 'failed'].includes(status)) {
      return NextResponse.json(
        { message: 'Invalid status' },
        { status: 400 }
      );
    }

    const { id } = await params;
    await connectDB();

    const payment = await Payment.findById(id);
    if (!payment) {
      return NextResponse.json(
        { message: 'Payment not found' },
        { status: 404 }
      );
    }

    // Mettre à jour le statut du paiement
    payment.status = status;
    await payment.save();

    // Si le paiement est complété, mettre à jour le statut de l'abonnement de l'utilisateur
    if (status === 'completed') {
      await User.findByIdAndUpdate(payment.userId, {
        subscriptionStatus: 'active',
        subscriptionEndDate: payment.endDate,
      });
    }

    return NextResponse.json(
      { message: 'Payment status updated successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error updating payment status:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
} 