'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { signOut } from 'next-auth/react';
import Button from '@/components/ui/Button';

const navigation = [
  { name: 'Tableau de bord', href: '/dashboard' },
  { name: 'Ren<PERSON>-vous', href: '/dashboard/bookings' },
  { name: '<PERSON><PERSON><PERSON>', href: '/dashboard/schedule' },
  { name: 'Site vitrine', href: '/dashboard/vitrine' },
  { name: 'Facturation', href: '/dashboard/billing' },
  { name: 'Paramètres', href: '/dashboard/settings' },
];

export default function DashboardNav() {
  const pathname = usePathname();

  return (
    <nav className="bg-white shadow">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link href="/dashboard" className="text-xl font-bold text-blue-600">
                RDV App
              </Link>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              {navigation.map((item) => {
                const isActive = pathname === item.href;
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                      isActive
                        ? 'border-blue-500 text-gray-900'
                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                    }`}
                  >
                    {item.name}
                  </Link>
                );
              })}
            </div>
          </div>
          <div className="hidden sm:ml-6 sm:flex sm:items-center">
            <Button
              variant="outline"
              onClick={() => signOut({ callbackUrl: '/' })}
            >
              Se déconnecter
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
} 