{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/RDV/rdv-app/src/components/DashboardNav.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/DashboardNav.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/DashboardNav.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/RDV/rdv-app/src/components/DashboardNav.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/DashboardNav.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/DashboardNav.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/RDV/rdv-app/src/components/ScheduleForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ScheduleForm.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScheduleForm.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/RDV/rdv-app/src/components/ScheduleForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ScheduleForm.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScheduleForm.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/RDV/rdv-app/src/app/dashboard/schedule/page.tsx"], "sourcesContent": ["import { getServerSession } from 'next-auth';\r\nimport { authOptions } from '@/lib/auth';\r\nimport { redirect } from 'next/navigation';\r\nimport connectDB from '@/lib/mongodb';\r\nimport User from '@/models/User';\r\nimport DashboardNav from '@/components/DashboardNav';\r\nimport ScheduleForm from '@/components/ScheduleForm';\r\n\r\nasync function getUserSchedule(userId: string) {\r\n  await connectDB();\r\n  const user = await User.findById(userId).select('schedule');\r\n\r\n  const defaultSchedule = {\r\n    monday: { isOpen: true, start: '09:00', end: '18:00' },\r\n    tuesday: { isOpen: true, start: '09:00', end: '18:00' },\r\n    wednesday: { isOpen: true, start: '09:00', end: '18:00' },\r\n    thursday: { isOpen: true, start: '09:00', end: '18:00' },\r\n    friday: { isOpen: true, start: '09:00', end: '18:00' },\r\n    saturday: { isOpen: true, start: '09:00', end: '18:00' },\r\n    sunday: { isOpen: false, start: '09:00', end: '18:00' },\r\n  };\r\n\r\n  return user?.schedule || defaultSchedule;\r\n}\r\n\r\nexport default async function SchedulePage() {\r\n  const session = await getServerSession(authOptions);\r\n\r\n  if (!session?.user?.id) {\r\n    redirect('/auth/signin');\r\n  }\r\n\r\n  const schedule = await getUserSchedule(session.user.id);\r\n\r\n  // Sérialiser l'objet schedule pour le passer au composant client\r\n  const serializedSchedule = JSON.parse(JSON.stringify(schedule));\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <DashboardNav />\r\n\r\n      <main className=\"py-10\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-3xl font-bold text-gray-900\">\r\n              Horaires d'ouverture\r\n            </h1>\r\n            <p className=\"mt-2 text-gray-600\">\r\n              Définissez vos horaires de travail pour chaque jour de la semaine\r\n            </p>\r\n          </div>\r\n\r\n          <ScheduleForm initialSchedule={serializedSchedule} />\r\n        </div>\r\n      </main>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,eAAe,gBAAgB,MAAc;IAC3C,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAS,AAAD;IACd,MAAM,OAAO,MAAM,qHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAAQ,MAAM,CAAC;IAEhD,MAAM,kBAAkB;QACtB,QAAQ;YAAE,QAAQ;YAAM,OAAO;YAAS,KAAK;QAAQ;QACrD,SAAS;YAAE,QAAQ;YAAM,OAAO;YAAS,KAAK;QAAQ;QACtD,WAAW;YAAE,QAAQ;YAAM,OAAO;YAAS,KAAK;QAAQ;QACxD,UAAU;YAAE,QAAQ;YAAM,OAAO;YAAS,KAAK;QAAQ;QACvD,QAAQ;YAAE,QAAQ;YAAM,OAAO;YAAS,KAAK;QAAQ;QACrD,UAAU;YAAE,QAAQ;YAAM,OAAO;YAAS,KAAK;QAAQ;QACvD,QAAQ;YAAE,QAAQ;YAAO,OAAO;YAAS,KAAK;QAAQ;IACxD;IAEA,OAAO,MAAM,YAAY;AAC3B;AAEe,eAAe;IAC5B,MAAM,UAAU,MAAM,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD,EAAE,kHAAA,CAAA,cAAW;IAElD,IAAI,CAAC,SAAS,MAAM,IAAI;QACtB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,MAAM,WAAW,MAAM,gBAAgB,QAAQ,IAAI,CAAC,EAAE;IAEtD,iEAAiE;IACjE,MAAM,qBAAqB,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;IAErD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,kIAAA,CAAA,UAAY;;;;;0BAEb,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CAGjD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;sCAKpC,8OAAC,kIAAA,CAAA,UAAY;4BAAC,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;AAKzC", "debugId": null}}]}