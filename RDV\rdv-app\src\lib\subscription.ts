import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import Booking from '@/models/Booking';

export interface SubscriptionLimits {
  maxBookingsPerMonth: number;
  maxServices: number;
  hasVitrineAccess: boolean;
  hasSMSNotifications: boolean;
  hasAdvancedReports: boolean;
}

export async function getUserSubscriptionLimits(userId: string): Promise<SubscriptionLimits> {
  await connectDB();
  
  const user = await User.findById(userId);
  if (!user) {
    throw new Error('Utilisateur non trouvé');
  }

  // Vérifier si l'abonnement a expiré
  const now = new Date();
  let isExpired = false;

  if (user.subscriptionStatus === 'trial' && user.trialEndDate && user.trialEndDate < now) {
    // Période d'essai expirée, passer en freemium
    await User.findByIdAndUpdate(userId, {
      subscriptionStatus: 'expired',
      subscriptionPlan: 'freemium',
      maxBookingsPerMonth: 10,
      maxServices: 3,
    });
    isExpired = true;
  } else if (user.subscriptionStatus === 'active' && user.subscriptionEndDate && user.subscriptionEndDate < now) {
    // Abonnement payant expiré, passer en freemium
    await User.findByIdAndUpdate(userId, {
      subscriptionStatus: 'expired',
      subscriptionPlan: 'freemium',
      maxBookingsPerMonth: 10,
      maxServices: 3,
    });
    isExpired = true;
  }

  // Définir les limites selon le plan
  let limits: SubscriptionLimits;

  if (isExpired || user.subscriptionPlan === 'freemium') {
    limits = {
      maxBookingsPerMonth: 10,
      maxServices: 3,
      hasVitrineAccess: false,
      hasSMSNotifications: false,
      hasAdvancedReports: false,
    };
  } else if (user.subscriptionPlan === 'premium') {
    limits = {
      maxBookingsPerMonth: -1, // Illimité
      maxServices: -1, // Illimité
      hasVitrineAccess: true,
      hasSMSNotifications: true,
      hasAdvancedReports: true,
    };
  } else if (user.subscriptionPlan === 'enterprise') {
    limits = {
      maxBookingsPerMonth: -1, // Illimité
      maxServices: -1, // Illimité
      hasVitrineAccess: true,
      hasSMSNotifications: true,
      hasAdvancedReports: true,
    };
  } else {
    // Plan trial ou autre
    limits = {
      maxBookingsPerMonth: user.maxBookingsPerMonth || 10,
      maxServices: user.maxServices || 3,
      hasVitrineAccess: user.subscriptionStatus === 'trial',
      hasSMSNotifications: user.subscriptionStatus === 'trial',
      hasAdvancedReports: user.subscriptionStatus === 'trial',
    };
  }

  return limits;
}

export async function checkBookingLimit(userId: string): Promise<{ canBook: boolean; currentCount: number; limit: number }> {
  const limits = await getUserSubscriptionLimits(userId);
  
  if (limits.maxBookingsPerMonth === -1) {
    return { canBook: true, currentCount: 0, limit: -1 };
  }

  // Compter les réservations du mois en cours
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);

  await connectDB();
  const currentCount = await Booking.countDocuments({
    businessId: userId,
    createdAt: {
      $gte: startOfMonth,
      $lte: endOfMonth,
    },
  });

  return {
    canBook: currentCount < limits.maxBookingsPerMonth,
    currentCount,
    limit: limits.maxBookingsPerMonth,
  };
}

export async function checkServiceLimit(userId: string, currentServiceCount: number): Promise<{ canAddService: boolean; limit: number }> {
  const limits = await getUserSubscriptionLimits(userId);
  
  if (limits.maxServices === -1) {
    return { canAddService: true, limit: -1 };
  }

  return {
    canAddService: currentServiceCount < limits.maxServices,
    limit: limits.maxServices,
  };
}

export function getSubscriptionStatusInfo(user: any) {
  const now = new Date();
  
  if (user.subscriptionStatus === 'trial') {
    const daysRemaining = user.trialEndDate ? 
      Math.max(0, Math.ceil((new Date(user.trialEndDate).getTime() - now.getTime()) / (1000 * 60 * 60 * 24))) : 0;
    
    return {
      status: 'trial',
      message: `Période d'essai - ${daysRemaining} jour${daysRemaining !== 1 ? 's' : ''} restant${daysRemaining !== 1 ? 's' : ''}`,
      isExpiringSoon: daysRemaining <= 7,
      daysRemaining,
    };
  }
  
  if (user.subscriptionStatus === 'active') {
    const daysRemaining = user.subscriptionEndDate ? 
      Math.max(0, Math.ceil((new Date(user.subscriptionEndDate).getTime() - now.getTime()) / (1000 * 60 * 60 * 24))) : 0;
    
    return {
      status: 'active',
      message: `Abonnement ${user.subscriptionPlan} actif`,
      isExpiringSoon: daysRemaining <= 7,
      daysRemaining,
    };
  }
  
  if (user.subscriptionStatus === 'expired') {
    return {
      status: 'expired',
      message: 'Abonnement expiré - Fonctionnalités limitées',
      isExpiringSoon: false,
      daysRemaining: 0,
    };
  }
  
  return {
    status: 'unknown',
    message: 'Statut d\'abonnement inconnu',
    isExpiringSoon: false,
    daysRemaining: 0,
  };
}
